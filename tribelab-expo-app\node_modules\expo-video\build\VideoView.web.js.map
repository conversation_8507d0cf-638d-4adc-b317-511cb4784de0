{"version": 3, "file": "VideoView.web.js", "sourceRoot": "", "sources": ["../src/VideoView.web.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,mBAAmB,EAAE,MAAM,OAAO,CAAC;AAClF,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAE1C,OAAoB,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AAG9D,SAAS,kBAAkB;IACzB,OAAO,OAAO,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;AAC1E,CAAC;AAED,SAAS,kBAAkB,CAAC,YAAiC;IAC3D,MAAM,YAAY,GAAG,YAAY,EAAE,UAAU,EAAE,IAAI,IAAI,CAAC;IAExD,IAAI,YAAY,IAAI,YAAY,EAAE,CAAC;QACjC,YAAY,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;QAC5B,YAAY,CAAC,OAAO,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;IACjD,CAAC;IACD,OAAO,YAAY,CAAC;AACtB,CAAC;AAED,SAAS,SAAS,CAAC,KAA8B;IAC/C,MAAM,eAAe,GAAG,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAClD,qIAAqI;IACrI,OAAO,eAAsC,CAAC;AAChD,CAAC;AAED,MAAM,UAAU,2BAA2B;IACzC,OAAO,OAAO,QAAQ,KAAK,QAAQ,IAAI,OAAO,QAAQ,CAAC,oBAAoB,KAAK,UAAU,CAAC;AAC7F,CAAC;AAED,MAAM,CAAC,MAAM,SAAS,GAAG,UAAU,CAAC,CAAC,KAAgD,EAAE,GAAG,EAAE,EAAE;IAC5F,MAAM,QAAQ,GAAG,MAAM,CAA0B,IAAI,CAAC,CAAC;IACvD,MAAM,YAAY,GAAG,MAAM,CAAqC,IAAI,CAAC,CAAC;IACtE,MAAM,sBAAsB,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IAC7C,MAAM,wBAAwB,GAAG,MAAM,CAAsB,IAAI,CAAC,CAAC;IACnE,MAAM,sBAAsB,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IAE7C;;;;;OAKG;IACH,MAAM,eAAe,GAAG,MAAM,CAAsB,IAAI,CAAC,CAAC;IAC1D,MAAM,eAAe,GAAG,MAAM,CAAkB,IAAI,CAAC,CAAC;IAEtD,mBAAmB,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QAC9B,eAAe,EAAE,KAAK,IAAI,EAAE;YAC1B,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC;gBAC5B,OAAO;YACT,CAAC;YACD,MAAM,QAAQ,CAAC,OAAO,EAAE,iBAAiB,EAAE,CAAC;QAC9C,CAAC;QACD,cAAc,EAAE,KAAK,IAAI,EAAE;YACzB,MAAM,QAAQ,CAAC,cAAc,EAAE,CAAC;QAClC,CAAC;QACD,qBAAqB,EAAE,KAAK,IAAI,EAAE;YAChC,MAAM,QAAQ,CAAC,OAAO,EAAE,uBAAuB,EAAE,CAAC;QACpD,CAAC;QACD,oBAAoB,EAAE,KAAK,IAAI,EAAE;YAC/B,IAAI,CAAC;gBACH,MAAM,QAAQ,CAAC,oBAAoB,EAAE,CAAC;YACxC,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,IAAI,CAAC,YAAY,YAAY,IAAI,CAAC,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;oBAChE,OAAO,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;gBACnE,CAAC;qBAAM,CAAC;oBACN,MAAM,CAAC,CAAC;gBACV,CAAC;YACH,CAAC;QACH,CAAC;KACF,CAAC,CAAC,CAAC;IAEJ,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,OAAO,GAAG,GAAG,EAAE;YACnB,KAAK,CAAC,uBAAuB,EAAE,EAAE,CAAC;QACpC,CAAC,CAAC;QACF,MAAM,OAAO,GAAG,GAAG,EAAE;YACnB,KAAK,CAAC,sBAAsB,EAAE,EAAE,CAAC;QACnC,CAAC,CAAC;QACF,MAAM,WAAW,GAAG,GAAG,EAAE;YACvB,sBAAsB,CAAC,OAAO,GAAG,IAAI,CAAC;QACxC,CAAC,CAAC;QACF,MAAM,SAAS,GAAG,GAAG,EAAE;YACrB,IAAI,sBAAsB,CAAC,OAAO,EAAE,CAAC;gBACnC,KAAK,CAAC,kBAAkB,EAAE,EAAE,CAAC;YAC/B,CAAC;YACD,sBAAsB,CAAC,OAAO,GAAG,KAAK,CAAC;QACzC,CAAC,CAAC;QACF,QAAQ,CAAC,OAAO,EAAE,gBAAgB,CAAC,uBAAuB,EAAE,OAAO,CAAC,CAAC;QACrE,QAAQ,CAAC,OAAO,EAAE,gBAAgB,CAAC,uBAAuB,EAAE,OAAO,CAAC,CAAC;QACrE,QAAQ,CAAC,OAAO,EAAE,gBAAgB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;QAC7D,QAAQ,CAAC,OAAO,EAAE,gBAAgB,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;QAE5D,OAAO,GAAG,EAAE;YACV,QAAQ,CAAC,OAAO,EAAE,mBAAmB,CAAC,uBAAuB,EAAE,OAAO,CAAC,CAAC;YACxE,QAAQ,CAAC,OAAO,EAAE,mBAAmB,CAAC,uBAAuB,EAAE,OAAO,CAAC,CAAC;YACxE,QAAQ,CAAC,OAAO,EAAE,mBAAmB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YAChE,QAAQ,CAAC,OAAO,EAAE,mBAAmB,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;QACjE,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,uBAAuB,CAAC,CAAC,CAAC;IAE5E,kHAAkH;IAClH,oCAAoC;IACpC,SAAS,gBAAgB;QACvB,MAAM,YAAY,GAAG,eAAe,CAAC,OAAO,CAAC;QAC7C,MAAM,YAAY,GAAG,eAAe,CAAC,OAAO,CAAC;QAC7C,MAAM,SAAS,GAAG,YAAY,CAAC,OAAO,CAAC;QAEvC,IAAI,YAAY,IAAI,YAAY,IAAI,SAAS,EAAE,CAAC;YAC9C,KAAK,CAAC,MAAM,CAAC,cAAc,CAAC,YAAY,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;QACrE,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,IAAI,CACV,uHAAuH,CACxH,CAAC;QACJ,CAAC;IACH,CAAC;IAED,SAAS,gBAAgB;QACvB,MAAM,YAAY,GAAG,eAAe,CAAC,OAAO,CAAC;QAC7C,MAAM,SAAS,GAAG,YAAY,CAAC,OAAO,CAAC;QACvC,IAAI,YAAY,IAAI,SAAS,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;YAClD,KAAK,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,OAAO,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAED,SAAS,sBAAsB;QAC7B,IACE,CAAC,sBAAsB,CAAC,OAAO;YAC/B,CAAC,SAAS,CAAC,cAAc,CAAC,aAAa;YACvC,CAAC,QAAQ,CAAC,OAAO,EACjB,CAAC;YACD,OAAO;QACT,CAAC;QACD,MAAM,YAAY,GAAG,kBAAkB,EAAE,CAAC;QAE1C,gBAAgB,EAAE,CAAC;QACnB,eAAe,CAAC,OAAO,GAAG,YAAY,CAAC;QACvC,eAAe,CAAC,OAAO,GAAG,kBAAkB,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QACtE,YAAY,CAAC,OAAO,GAAG,YAAY;YACjC,CAAC,CAAC,YAAY,CAAC,wBAAwB,CAAC,QAAQ,CAAC,OAAO,CAAC;YACzD,CAAC,CAAC,IAAI,CAAC;QACT,gBAAgB,EAAE,CAAC;QACnB,sBAAsB,CAAC,OAAO,GAAG,KAAK,CAAC;IACzC,CAAC;IAED,SAAS,kBAAkB;QACzB,IAAI,QAAQ,CAAC,iBAAiB,KAAK,QAAQ,CAAC,OAAO,EAAE,CAAC;YACpD,KAAK,CAAC,iBAAiB,EAAE,EAAE,CAAC;QAC9B,CAAC;aAAM,CAAC;YACN,KAAK,CAAC,gBAAgB,EAAE,EAAE,CAAC;QAC7B,CAAC;IACH,CAAC;IAED,SAAS,uBAAuB;QAC9B,wBAAwB,CAAC,OAAO,GAAG,kBAAkB,CAAC;QACtD,QAAQ,CAAC,OAAO,EAAE,gBAAgB,CAAC,kBAAkB,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;IAC3F,CAAC;IAED,SAAS,yBAAyB;QAChC,IAAI,wBAAwB,CAAC,OAAO,EAAE,CAAC;YACrC,QAAQ,CAAC,OAAO,EAAE,mBAAmB,CAAC,kBAAkB,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC;YAC5F,wBAAwB,CAAC,OAAO,GAAG,IAAI,CAAC;QAC1C,CAAC;IACH,CAAC;IAED,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;YACrB,KAAK,CAAC,MAAM,EAAE,cAAc,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QACjD,CAAC;QACD,uBAAuB,EAAE,CAAC;QAC1B,gBAAgB,EAAE,CAAC;QAEnB,OAAO,GAAG,EAAE;YACV,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACrB,KAAK,CAAC,MAAM,EAAE,gBAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACnD,CAAC;YACD,yBAAyB,EAAE,CAAC;YAC5B,gBAAgB,EAAE,CAAC;QACrB,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IAEnB,OAAO,CACL,CAAC,KAAK,CACJ,QAAQ,CAAC,CAAC,KAAK,CAAC,cAAc,IAAI,IAAI,CAAC,CACvC,YAAY,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,cAAc,CAAC,CAClE,WAAW,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAC/B,KAAK,CAAC,CAAC;YACL,GAAG,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC;YACzB,SAAS,EAAE,KAAK,CAAC,UAAU;SAC5B,CAAC,CACF,MAAM,CAAC,CAAC,GAAG,EAAE;YACX,sBAAsB,EAAE,CAAC;QAC3B,CAAC,CAAC;IACF,yFAAyF;IACzF,cAAc,CAAC,CAAC,GAAG,EAAE;YACnB,sBAAsB,EAAE,CAAC;QAC3B,CAAC,CAAC,CACF,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,EAAE;YACd,+EAA+E;YAC/E,6EAA6E;YAC7E,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBACpD,QAAQ,CAAC,OAAO,GAAG,MAAM,CAAC;gBAC1B,sBAAsB,CAAC,OAAO,GAAG,IAAI,CAAC;gBACtC,sBAAsB,EAAE,CAAC;YAC3B,CAAC;QACH,CAAC,CAAC,CACF,uBAAuB,CAAC,CAAC,CAAC,KAAK,CAAC,sBAAsB,CAAC,CACvD,WAAW,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAC/B,GAAG,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,EAC3C,CACH,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,eAAe,SAAS,CAAC", "sourcesContent": ["import React, { useEffect, useRef, forwardRef, useImperativeHandle } from 'react';\nimport { StyleSheet } from 'react-native';\n\nimport VideoPlayer, { getSourceUri } from './VideoPlayer.web';\nimport type { VideoViewProps } from './VideoView.types';\n\nfunction createAudioContext(): AudioContext | null {\n  return typeof window !== 'undefined' ? new window.AudioContext() : null;\n}\n\nfunction createZeroGainNode(audioContext: AudioContext | null): GainNode | null {\n  const zeroGainNode = audioContext?.createGain() ?? null;\n\n  if (audioContext && zeroGainNode) {\n    zeroGainNode.gain.value = 0;\n    zeroGainNode.connect(audioContext.destination);\n  }\n  return zeroGainNode;\n}\n\nfunction mapStyles(style: VideoViewProps['style']): React.CSSProperties {\n  const flattenedStyles = StyleSheet.flatten(style);\n  // Looking through react-native-web source code they also just pass styles directly without further conversions, so it's just a cast.\n  return flattenedStyles as React.CSSProperties;\n}\n\nexport function isPictureInPictureSupported(): boolean {\n  return typeof document === 'object' && typeof document.exitPictureInPicture === 'function';\n}\n\nexport const VideoView = forwardRef((props: { player?: VideoPlayer } & VideoViewProps, ref) => {\n  const videoRef = useRef<null | HTMLVideoElement>(null);\n  const mediaNodeRef = useRef<null | MediaElementAudioSourceNode>(null);\n  const hasToSetupAudioContext = useRef(false);\n  const fullscreenChangeListener = useRef<null | (() => void)>(null);\n  const isWaitingForFirstFrame = useRef(false);\n\n  /**\n   * Audio context is used to mute all but one video when multiple video views are playing from one player simultaneously.\n   * Using audio context nodes allows muting videos without displaying the mute icon in the video player.\n   * We have to keep the context that called createMediaElementSource(videoRef), as the method can't be called\n   * for the second time with another context and there is no way to unbind the video and audio context afterward.\n   */\n  const audioContextRef = useRef<null | AudioContext>(null);\n  const zeroGainNodeRef = useRef<null | GainNode>(null);\n\n  useImperativeHandle(ref, () => ({\n    enterFullscreen: async () => {\n      if (!props.allowsFullscreen) {\n        return;\n      }\n      await videoRef.current?.requestFullscreen();\n    },\n    exitFullscreen: async () => {\n      await document.exitFullscreen();\n    },\n    startPictureInPicture: async () => {\n      await videoRef.current?.requestPictureInPicture();\n    },\n    stopPictureInPicture: async () => {\n      try {\n        await document.exitPictureInPicture();\n      } catch (e) {\n        if (e instanceof DOMException && e.name === 'InvalidStateError') {\n          console.warn('The VideoView is not in Picture-in-Picture mode.');\n        } else {\n          throw e;\n        }\n      }\n    },\n  }));\n\n  useEffect(() => {\n    const onEnter = () => {\n      props.onPictureInPictureStart?.();\n    };\n    const onLeave = () => {\n      props.onPictureInPictureStop?.();\n    };\n    const onLoadStart = () => {\n      isWaitingForFirstFrame.current = true;\n    };\n    const onCanPlay = () => {\n      if (isWaitingForFirstFrame.current) {\n        props.onFirstFrameRender?.();\n      }\n      isWaitingForFirstFrame.current = false;\n    };\n    videoRef.current?.addEventListener('enterpictureinpicture', onEnter);\n    videoRef.current?.addEventListener('leavepictureinpicture', onLeave);\n    videoRef.current?.addEventListener('loadstart', onLoadStart);\n    videoRef.current?.addEventListener('loadeddata', onCanPlay);\n\n    return () => {\n      videoRef.current?.removeEventListener('enterpictureinpicture', onEnter);\n      videoRef.current?.removeEventListener('leavepictureinpicture', onLeave);\n      videoRef.current?.removeEventListener('loadstart', onLoadStart);\n      videoRef.current?.removeEventListener('loadeddata', onCanPlay);\n    };\n  }, [videoRef, props.onPictureInPictureStop, props.onPictureInPictureStart]);\n\n  // Adds the video view as a candidate for being the audio source for the player (when multiple views play from one\n  // player only one will emit audio).\n  function attachAudioNodes() {\n    const audioContext = audioContextRef.current;\n    const zeroGainNode = zeroGainNodeRef.current;\n    const mediaNode = mediaNodeRef.current;\n\n    if (audioContext && zeroGainNode && mediaNode) {\n      props.player.mountAudioNode(audioContext, zeroGainNode, mediaNode);\n    } else {\n      console.warn(\n        \"Couldn't mount audio node, this might affect the audio playback when using multiple video views with the same player.\"\n      );\n    }\n  }\n\n  function detachAudioNodes() {\n    const audioContext = audioContextRef.current;\n    const mediaNode = mediaNodeRef.current;\n    if (audioContext && mediaNode && videoRef.current) {\n      props.player.unmountAudioNode(videoRef.current, audioContext, mediaNode);\n    }\n  }\n\n  function maybeSetupAudioContext() {\n    if (\n      !hasToSetupAudioContext.current ||\n      !navigator.userActivation.hasBeenActive ||\n      !videoRef.current\n    ) {\n      return;\n    }\n    const audioContext = createAudioContext();\n\n    detachAudioNodes();\n    audioContextRef.current = audioContext;\n    zeroGainNodeRef.current = createZeroGainNode(audioContextRef.current);\n    mediaNodeRef.current = audioContext\n      ? audioContext.createMediaElementSource(videoRef.current)\n      : null;\n    attachAudioNodes();\n    hasToSetupAudioContext.current = false;\n  }\n\n  function fullscreenListener() {\n    if (document.fullscreenElement === videoRef.current) {\n      props.onFullscreenEnter?.();\n    } else {\n      props.onFullscreenExit?.();\n    }\n  }\n\n  function setupFullscreenListener() {\n    fullscreenChangeListener.current = fullscreenListener;\n    videoRef.current?.addEventListener('fullscreenchange', fullscreenChangeListener.current);\n  }\n\n  function cleanupFullscreenListener() {\n    if (fullscreenChangeListener.current) {\n      videoRef.current?.removeEventListener('fullscreenchange', fullscreenChangeListener.current);\n      fullscreenChangeListener.current = null;\n    }\n  }\n\n  useEffect(() => {\n    if (videoRef.current) {\n      props.player?.mountVideoView(videoRef.current);\n    }\n    setupFullscreenListener();\n    attachAudioNodes();\n\n    return () => {\n      if (videoRef.current) {\n        props.player?.unmountVideoView(videoRef.current);\n      }\n      cleanupFullscreenListener();\n      detachAudioNodes();\n    };\n  }, [props.player]);\n\n  return (\n    <video\n      controls={props.nativeControls ?? true}\n      controlsList={props.allowsFullscreen ? undefined : 'nofullscreen'}\n      crossOrigin={props.crossOrigin}\n      style={{\n        ...mapStyles(props.style),\n        objectFit: props.contentFit,\n      }}\n      onPlay={() => {\n        maybeSetupAudioContext();\n      }}\n      // The player can autoplay when muted, unmuting by a user should create the audio context\n      onVolumeChange={() => {\n        maybeSetupAudioContext();\n      }}\n      ref={(newRef) => {\n        // This is called with a null value before `player.unmountVideoView` is called,\n        // we can't assign null to videoRef if we want to unmount it from the player.\n        if (newRef && !newRef.isEqualNode(videoRef.current)) {\n          videoRef.current = newRef;\n          hasToSetupAudioContext.current = true;\n          maybeSetupAudioContext();\n        }\n      }}\n      disablePictureInPicture={!props.allowsPictureInPicture}\n      playsInline={props.playsInline}\n      src={getSourceUri(props.player?.src) ?? ''}\n    />\n  );\n});\n\nexport default VideoView;\n"]}