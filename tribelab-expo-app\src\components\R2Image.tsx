import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ActivityIndicator, Text } from 'react-native';
import { Image, ImageSource } from 'expo-image';
import { getOptimizedImageUrl, getThumbnailUrl } from '../utils/r2Storage';

interface R2ImageProps {
  source: string | ImageSource;
  style?: any;
  width?: number;
  height?: number;
  quality?: number;
  format?: 'webp' | 'jpeg' | 'png';
  useThumbnail?: boolean;
  contentFit?: 'cover' | 'contain' | 'fill' | 'scale-down' | 'none';
  transition?: number;
  placeholder?: string;
  fallbackSource?: ImageSource;
  onLoad?: () => void;
  onError?: (error: any) => void;
  showLoadingIndicator?: boolean;
  borderRadius?: number;
  cachePolicy?: 'memory' | 'disk' | 'memory-disk' | 'none';
}

const R2Image: React.FC<R2ImageProps> = ({
  source,
  style,
  width,
  height,
  quality = 85,
  format = 'webp',
  useThumbnail = false,
  contentFit = 'cover',
  transition = 200,
  placeholder,
  fallbackSource,
  onLoad,
  onError,
  showLoadingIndicator = true,
  borderRadius,
  cachePolicy = 'memory-disk',
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [imageSource, setImageSource] = useState<ImageSource | null>(null);

  useEffect(() => {
    if (typeof source === 'string') {
      // Optimize R2 image URL
      let optimizedUrl: string;
      
      if (useThumbnail) {
        optimizedUrl = getThumbnailUrl(source);
      } else {
        optimizedUrl = getOptimizedImageUrl(source, {
          width,
          height,
          quality,
          format,
        });
      }

      setImageSource({ uri: optimizedUrl });
    } else {
      setImageSource(source);
    }
  }, [source, width, height, quality, format, useThumbnail]);

  const handleLoad = () => {
    setLoading(false);
    setError(false);
    onLoad?.();
  };

  const handleError = (errorEvent: any) => {
    setLoading(false);
    setError(true);
    onError?.(errorEvent);
  };

  const renderLoadingIndicator = () => {
    if (!showLoadingIndicator || !loading) return null;

    return (
      <View style={[styles.loadingContainer, style, { borderRadius }]}>
        <ActivityIndicator size="small" color="#007AFF" />
      </View>
    );
  };

  const renderErrorFallback = () => {
    if (!error) return null;

    if (fallbackSource) {
      return (
        <Image
          source={fallbackSource}
          style={[style, { borderRadius }]}
          contentFit={contentFit}
          transition={transition}
          cachePolicy={cachePolicy}
        />
      );
    }

    return (
      <View style={[styles.errorContainer, style, { borderRadius }]}>
        <View style={styles.errorIcon}>
          <View style={styles.errorIconInner} />
        </View>
      </View>
    );
  };

  if (error) {
    return renderErrorFallback();
  }

  return (
    <View style={[styles.container, { borderRadius }]}>
      {imageSource && (
        <Image
          source={imageSource}
          style={[style, { borderRadius }]}
          contentFit={contentFit}
          transition={transition}
          onLoad={handleLoad}
          onError={handleError}
          cachePolicy={cachePolicy}
          placeholder={placeholder}
        />
      )}
      {renderLoadingIndicator()}
    </View>
  );
};

// Avatar component using R2Image
interface AvatarProps {
  source?: string;
  size?: number;
  name?: string;
  style?: any;
  onPress?: () => void;
}

export const Avatar: React.FC<AvatarProps> = ({
  source,
  size = 40,
  name,
  style,
  onPress,
}) => {
  const getInitials = (fullName?: string) => {
    if (!fullName) return '?';
    
    const names = fullName.trim().split(' ');
    if (names.length === 1) {
      return names[0].charAt(0).toUpperCase();
    }
    
    return (names[0].charAt(0) + names[names.length - 1].charAt(0)).toUpperCase();
  };

  const avatarStyle = {
    width: size,
    height: size,
    borderRadius: size / 2,
  };

  if (source) {
    return (
      <R2Image
        source={source}
        style={[avatarStyle, style]}
        width={size * 2} // 2x for retina
        height={size * 2}
        useThumbnail={size < 100}
        contentFit="cover"
        borderRadius={size / 2}
      />
    );
  }

  // Fallback to initials
  return (
    <View style={[styles.initialsContainer, avatarStyle, style]}>
      <Text style={[styles.initialsText, { fontSize: size * 0.4 }]}>
        {getInitials(name)}
      </Text>
    </View>
  );
};

// Thumbnail component for lists
interface ThumbnailProps {
  source: string;
  width: number;
  height: number;
  style?: any;
  onPress?: () => void;
}

export const Thumbnail: React.FC<ThumbnailProps> = ({
  source,
  width,
  height,
  style,
  onPress,
}) => {
  return (
    <R2Image
      source={source}
      style={[{ width, height }, style]}
      width={width * 2}
      height={height * 2}
      useThumbnail={true}
      quality={75}
      contentFit="cover"
      showLoadingIndicator={true}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  errorContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
  },
  errorIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#ccc',
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorIconInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#999',
  },
  initialsContainer: {
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  initialsText: {
    color: 'white',
    fontWeight: 'bold',
  },
});

export default R2Image;
