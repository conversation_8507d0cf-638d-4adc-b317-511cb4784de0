{"version": 3, "file": "VideoPlayerEvents.types.d.ts", "sourceRoot": "", "sources": ["../src/VideoPlayerEvents.types.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,WAAW,EACX,aAAa,EACb,iBAAiB,EACjB,WAAW,EACX,UAAU,EACV,UAAU,EACX,MAAM,qBAAqB,CAAC;AAE7B;;GAEG;AACH,MAAM,MAAM,iBAAiB,GAAG;IAC9B;;OAEG;IACH,YAAY,CAAC,OAAO,EAAE,wBAAwB,GAAG,IAAI,CAAC;IAEtD;;OAEG;IACH,aAAa,CAAC,OAAO,EAAE,yBAAyB,GAAG,IAAI,CAAC;IAExD;;OAEG;IACH,kBAAkB,CAAC,OAAO,EAAE,8BAA8B,GAAG,IAAI,CAAC;IAElE;;OAEG;IACH,YAAY,CAAC,OAAO,EAAE,wBAAwB,GAAG,IAAI,CAAC;IAEtD;;OAEG;IACH,WAAW,CAAC,OAAO,EAAE,uBAAuB,GAAG,IAAI,CAAC;IAEpD;;OAEG;IACH,SAAS,IAAI,IAAI,CAAC;IAElB;;OAEG;IACH,UAAU,CAAC,OAAO,EAAE,sBAAsB,GAAG,IAAI,CAAC;IAElD;;OAEG;IACH,YAAY,CAAC,OAAO,EAAE,wBAAwB,GAAG,IAAI,CAAC;IAEtD;;OAEG;IACH,6BAA6B,CAAC,OAAO,EAAE,yCAAyC,GAAG,IAAI,CAAC;IAExF;;OAEG;IACH,mBAAmB,CAAC,OAAO,EAAE,+BAA+B,GAAG,IAAI,CAAC;IAEpE;;OAEG;IACH,0BAA0B,CAAC,OAAO,EAAE,sCAAsC,GAAG,IAAI,CAAC;IAElF;;OAEG;IACH,gBAAgB,CAAC,OAAO,EAAE,4BAA4B,GAAG,IAAI,CAAC;IAE9D;;OAEG;IACH,gBAAgB,CAAC,OAAO,EAAE,4BAA4B,GAAG,IAAI,CAAC;IAE9D;;;OAGG;IACH,UAAU,CAAC,OAAO,EAAE,sBAAsB,GAAG,IAAI,CAAC;CACnD,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,wBAAwB,GAAG;IACrC;;OAEG;IACH,MAAM,EAAE,iBAAiB,CAAC;IAE1B;;OAEG;IACH,SAAS,CAAC,EAAE,iBAAiB,CAAC;IAE9B;;OAEG;IACH,KAAK,CAAC,EAAE,WAAW,CAAC;CACrB,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,yBAAyB,GAAG;IACtC;;OAEG;IACH,SAAS,EAAE,OAAO,CAAC;IAEnB;;OAEG;IACH,YAAY,CAAC,EAAE,OAAO,CAAC;CACxB,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,8BAA8B,GAAG;IAC3C;;OAEG;IACH,YAAY,EAAE,MAAM,CAAC;IAErB;;OAEG;IACH,eAAe,CAAC,EAAE,MAAM,CAAC;CAC1B,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,wBAAwB,GAAG;IACrC;;OAEG;IACH,MAAM,EAAE,MAAM,CAAC;IAEf;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC;CACpB,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,uBAAuB,GAAG;IACpC;;OAEG;IACH,KAAK,EAAE,OAAO,CAAC;IAEf;;OAEG;IACH,QAAQ,CAAC,EAAE,OAAO,CAAC;CACpB,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,wBAAwB,GAAG;IACrC;;OAEG;IACH,MAAM,EAAE,WAAW,CAAC;IAEpB;;OAEG;IACH,SAAS,CAAC,EAAE,WAAW,CAAC;CACzB,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,sBAAsB,GAAG;IACnC;;OAEG;IACH,WAAW,EAAE,MAAM,CAAC;IAEpB;;;;;;OAMG;IACH,oBAAoB,EAAE,MAAM,GAAG,IAAI,CAAC;IAEpC;;;;;OAKG;IACH,qBAAqB,EAAE,MAAM,GAAG,IAAI,CAAC;IAErC;;;;;OAKG;IACH,gBAAgB,EAAE,MAAM,CAAC;CAC1B,CAAC;AAEF,MAAM,MAAM,+BAA+B,GAAG;IAC5C;;OAEG;IACH,aAAa,EAAE,aAAa,GAAG,IAAI,CAAC;IAEpC;;OAEG;IACH,gBAAgB,CAAC,EAAE,aAAa,GAAG,IAAI,CAAC;CACzC,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,4BAA4B,GAAG;IACzC;;OAEG;IACH,UAAU,EAAE,UAAU,GAAG,IAAI,CAAC;IAE9B;;OAEG;IACH,aAAa,CAAC,EAAE,UAAU,GAAG,IAAI,CAAC;CACnC,CAAC;AAEF;;;GAGG;AACH,MAAM,MAAM,yCAAyC,GAAG;IACtD;;OAEG;IACH,uBAAuB,EAAE,aAAa,EAAE,CAAC;IAEzC;;OAEG;IACH,0BAA0B,CAAC,EAAE,aAAa,EAAE,CAAC;CAC9C,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,sBAAsB,GAAG;IACnC;;OAEG;IACH,WAAW,EAAE,WAAW,GAAG,IAAI,CAAC;IAEhC;;OAEG;IACH,QAAQ,EAAE,MAAM,CAAC;IAEjB;;;;OAIG;IACH,oBAAoB,EAAE,UAAU,EAAE,CAAC;IAEnC;;OAEG;IACH,uBAAuB,EAAE,aAAa,EAAE,CAAC;IAEzC;;OAEG;IACH,oBAAoB,EAAE,UAAU,EAAE,CAAC;CACpC,CAAC;AAEF,KAAK,4BAA4B,GAAG;IAClC;;OAEG;IACH,UAAU,EAAE,UAAU,GAAG,IAAI,CAAC;IAE9B;;OAEG;IACH,aAAa,CAAC,EAAE,UAAU,GAAG,IAAI,CAAC;CACnC,CAAC;AAEF,KAAK,sCAAsC,GAAG;IAC5C;;OAEG;IACH,oBAAoB,EAAE,UAAU,EAAE,CAAC;IAEnC;;OAEG;IACH,uBAAuB,CAAC,EAAE,UAAU,EAAE,CAAC;CACxC,CAAC"}