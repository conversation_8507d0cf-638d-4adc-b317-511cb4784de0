#!/usr/bin/env node

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkEnvironment() {
  const envPath = join(__dirname, '.env');
  
  if (!fs.existsSync(envPath)) {
    log('⚠️  .env file not found. Creating from .env.example...', 'yellow');
    
    const examplePath = join(__dirname, '.env.example');
    if (fs.existsSync(examplePath)) {
      fs.copyFileSync(examplePath, envPath);
      log('✅ .env file created successfully', 'green');
      log('📝 Please review and update the .env file with your configuration', 'cyan');
    } else {
      log('❌ .env.example file not found', 'red');
      process.exit(1);
    }
  }
}

function checkMongoDB() {
  return new Promise((resolve) => {
    log('🔍 Checking MongoDB connection...', 'cyan');
    
    // Try to connect to MongoDB
    const mongoCheck = spawn('node', ['-e', `
      import mongoose from 'mongoose';
      import dotenv from 'dotenv';
      dotenv.config();
      
      mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/thetribelabofficial')
        .then(() => {
          console.log('MongoDB connection successful');
          process.exit(0);
        })
        .catch((err) => {
          console.error('MongoDB connection failed:', err.message);
          process.exit(1);
        });
    `], { cwd: __dirname });

    mongoCheck.on('close', (code) => {
      if (code === 0) {
        log('✅ MongoDB connection successful', 'green');
        resolve(true);
      } else {
        log('❌ MongoDB connection failed', 'red');
        log('💡 Make sure MongoDB is running and the connection string is correct', 'yellow');
        log('   Default: mongodb://localhost:27017/thetribelabofficial', 'yellow');
        resolve(false);
      }
    });
  });
}

async function startServer() {
  log('🚀 Starting TribeLab Mobile Backend...', 'bright');
  log('=' .repeat(50), 'cyan');
  
  // Check environment
  checkEnvironment();
  
  // Check MongoDB connection
  const mongoConnected = await checkMongoDB();
  
  if (!mongoConnected) {
    log('⚠️  Continuing without MongoDB verification...', 'yellow');
  }
  
  log('🔄 Starting Express server...', 'cyan');
  
  // Start the server
  const server = spawn('node', ['server.js'], { 
    cwd: __dirname,
    stdio: 'inherit'
  });
  
  server.on('error', (err) => {
    log(`❌ Failed to start server: ${err.message}`, 'red');
    process.exit(1);
  });
  
  server.on('close', (code) => {
    if (code !== 0) {
      log(`❌ Server exited with code ${code}`, 'red');
    } else {
      log('👋 Server stopped gracefully', 'green');
    }
  });
  
  // Handle process termination
  process.on('SIGINT', () => {
    log('\n🛑 Shutting down server...', 'yellow');
    server.kill('SIGINT');
  });
  
  process.on('SIGTERM', () => {
    log('\n🛑 Shutting down server...', 'yellow');
    server.kill('SIGTERM');
  });
}

// Start the application
startServer().catch((err) => {
  log(`❌ Failed to start application: ${err.message}`, 'red');
  process.exit(1);
});
