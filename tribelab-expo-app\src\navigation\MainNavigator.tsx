import React from "react";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { createStackNavigator } from "@react-navigation/stack";
import Icon from "react-native-vector-icons/Ionicons";

// Main Tab Screens
import HomeScreen from "../screens/HomeScreen";
import ChatScreen from "../screens/ChatScreen";
import NotificationsScreen from "../screens/NotificationsScreen";
import NotificationSettingsScreen from "../screens/NotificationSettingsScreen";
import ProfileScreen from "../screens/ProfileScreen";

// Stack Navigators
import CommunityStackNavigator from "./CommunityStackNavigator";
import CourseStackNavigator from "./CourseStackNavigator";
import AdminStackNavigator from "./AdminStackNavigator";
import SettingsStackNavigator from "./SettingsStackNavigator";
import SearchStackNavigator from "./SearchStackNavigator";

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

// Home Stack Navigator - Main feed and discovery
const HomeStack = () => (
  <Stack.Navigator screenOptions={{ headerShown: false }}>
    <Stack.Screen name="HomeMain" component={HomeScreen} />
    <Stack.Screen name="Courses" component={CourseStackNavigator} />
    <Stack.Screen name="Search" component={SearchStackNavigator} />
    <Stack.Screen name="Settings" component={SettingsStackNavigator} />
    <Stack.Screen name="Admin" component={AdminStackNavigator} />
  </Stack.Navigator>
);

// Chat Stack Navigator - Messaging and communication
const ChatStack = () => (
  <Stack.Navigator screenOptions={{ headerShown: false }}>
    <Stack.Screen name="ChatMain" component={ChatScreen} />
    {/* Future: ChatListScreen, ChatDetailScreen, GroupChatScreen */}
  </Stack.Navigator>
);

// Notifications Stack Navigator - Alerts and updates
const NotificationsStack = () => (
  <Stack.Navigator screenOptions={{ headerShown: false }}>
    <Stack.Screen name="NotificationsMain" component={NotificationsScreen} />
    <Stack.Screen
      name="NotificationSettings"
      component={NotificationSettingsScreen}
    />
  </Stack.Navigator>
);

// Profile Stack Navigator - User profile and account
const ProfileStack = () => (
  <Stack.Navigator screenOptions={{ headerShown: false }}>
    <Stack.Screen name="ProfileMain" component={ProfileScreen} />
    <Stack.Screen name="Settings" component={SettingsStackNavigator} />
    <Stack.Screen name="Admin" component={AdminStackNavigator} />
  </Stack.Navigator>
);

const MainNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName = "";

          if (route.name === "Home") {
            iconName = focused ? "home" : "home-outline";
          } else if (route.name === "Chat") {
            iconName = focused ? "chatbubble" : "chatbubble-outline";
          } else if (route.name === "Notifications") {
            iconName = focused ? "notifications" : "notifications-outline";
          } else if (route.name === "Profile") {
            iconName = focused ? "person" : "person-outline";
          } else if (route.name === "Community") {
            iconName = focused ? "people" : "people-outline";
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: "#000",
        tabBarInactiveTintColor: "gray",
        headerShown: false,
        tabBarStyle: {
          height: 60,
          paddingBottom: 10,
          paddingTop: 5,
        },
      })}
    >
      <Tab.Screen name="Home" component={HomeStack} />
      <Tab.Screen name="Chat" component={ChatStack} />
      <Tab.Screen
        name="Community"
        component={CommunityStackNavigator}
        options={{
          tabBarButton: (props) => <TabBarCenterButton {...props} />,
        }}
      />
      <Tab.Screen
        name="Notifications"
        component={NotificationsStack}
        options={{
          tabBarBadge: 99,
          tabBarBadgeStyle: { backgroundColor: "#ff4040", color: "white" },
        }}
      />
      <Tab.Screen name="Profile" component={ProfileStack} />
    </Tab.Navigator>
  );
};

// Custom center button component (placeholder - will be styled to match the UI)
import { TouchableOpacity, View, StyleSheet } from "react-native";

const TabBarCenterButton = ({ accessibilityState, onPress }: any) => {
  const focused = accessibilityState?.selected || false;

  return (
    <TouchableOpacity onPress={onPress} style={styles.centerButtonContainer}>
      <View
        style={[
          styles.centerButton,
          { backgroundColor: focused ? "#000" : "#333" },
        ]}
      >
        <Icon name="add" size={30} color="white" />
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  centerButtonContainer: {
    position: "absolute",
    bottom: 0,
    height: 80,
    width: 80,
    justifyContent: "center",
    alignItems: "center",
  },
  centerButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: "#000",
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 5,
  },
});

export default MainNavigator;
