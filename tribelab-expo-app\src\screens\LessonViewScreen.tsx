import React, { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Dimensions,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useNavigation, useRoute } from "@react-navigation/native";
import { VideoView, useVideoPlayer } from "expo-video";
import { WebView } from "react-native-webview";

interface Lesson {
  _id: string;
  title: string;
  description?: string;
  content?: string;
  videoUrl?: string;
  attachments?: {
    name: string;
    url: string;
    type: string;
    size?: number;
  }[];
  moduleId: string;
  courseId: string;
  order: number;
  isPublished: boolean;
}

interface Module {
  _id: string;
  title: string;
  order: number;
}

interface Course {
  _id: string;
  title: string;
  modules: Module[];
}

interface Navigation {
  previousLesson?: { _id: string; title: string };
  nextLesson?: { _id: string; title: string };
}

interface Progress {
  completedLessons: string[];
  currentLesson?: string;
}

const { width: screenWidth } = Dimensions.get("window");

const LessonViewScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { lessonId, courseId, slug } = route.params as {
    lessonId: string;
    courseId: string;
    slug: string;
  };

  const [lesson, setLesson] = useState<Lesson | null>(null);
  const [course, setCourse] = useState<Course | null>(null);
  const [lessonNavigation, setLessonNavigation] = useState<Navigation | null>(
    null
  );
  const [progress, setProgress] = useState<Progress | null>(null);
  const [loading, setLoading] = useState(true);
  const [isCompleted, setIsCompleted] = useState(false);
  const [markingComplete, setMarkingComplete] = useState(false);
  const [showSidebar, setShowSidebar] = useState(false);
  const [allLessons, setAllLessons] = useState<
    {
      moduleId: string;
      moduleTitle: string;
      lessons: { _id: string; title: string; isCompleted: boolean }[];
    }[]
  >([]);

  const videoRef = useRef<VideoView>(null);

  useEffect(() => {
    fetchLessonData();
  }, [lessonId]);

  const fetchLessonData = async () => {
    try {
      const token = await AsyncStorage.getItem("auth_token");
      if (!token) {
        Alert.alert("Error", "Please login again");
        return;
      }

      // Fetch lesson details
      const lessonResponse = await fetch(
        `https://api.tribelab.com/api/lessons/${lessonId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (lessonResponse.ok) {
        const lessonData = await lessonResponse.json();
        setLesson(lessonData);

        // Fetch course details and navigation
        const courseResponse = await fetch(
          `https://api.tribelab.com/api/courses/${courseId}`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (courseResponse.ok) {
          const courseData = await courseResponse.json();
          setCourse(courseData);

          // Group lessons by module for sidebar
          const modules = courseData.modules.sort(
            (a: any, b: any) => a.order - b.order
          );
          const lessons = courseData.lessons;

          const groupedLessons = modules.map((mod: any) => {
            const moduleLessons = lessons
              .filter((l: any) => l.moduleId === mod._id)
              .sort((a: any, b: any) => a.order - b.order)
              .map((l: any) => ({
                _id: l._id,
                title: l.title,
                isCompleted: false, // Will be updated with progress data
              }));

            return {
              moduleId: mod._id,
              moduleTitle: mod.title,
              lessons: moduleLessons,
            };
          });

          setAllLessons(groupedLessons);
        }

        // Fetch progress
        const progressResponse = await fetch(
          `https://api.tribelab.com/api/progress/${courseId}`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (progressResponse.ok) {
          const progressData = await progressResponse.json();
          setProgress(progressData);
          setIsCompleted(
            progressData.completedLessons?.includes(lessonId) || false
          );

          // Update lesson completion status in sidebar
          setAllLessons((prev) =>
            prev.map((module) => ({
              ...module,
              lessons: module.lessons.map((l) => ({
                ...l,
                isCompleted:
                  progressData.completedLessons?.includes(l._id) || false,
              })),
            }))
          );
        }

        // Fetch navigation (previous/next lessons)
        const navResponse = await fetch(
          `https://api.tribelab.com/api/lessons/${lessonId}/navigation`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (navResponse.ok) {
          const navData = await navResponse.json();
          setLessonNavigation(navData);
        }
      } else {
        throw new Error("Failed to fetch lesson");
      }
    } catch (error) {
      console.error("Error fetching lesson:", error);
      Alert.alert("Error", "Failed to load lesson");
    } finally {
      setLoading(false);
    }
  };

  const markLessonComplete = async () => {
    if (isCompleted) return;

    setMarkingComplete(true);
    try {
      const token = await AsyncStorage.getItem("auth_token");
      const response = await fetch(
        `https://api.tribelab.com/api/progress/${courseId}/lesson/${lessonId}`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (response.ok) {
        setIsCompleted(true);
        Alert.alert("Success", "Lesson marked as complete!");

        // Update progress and sidebar
        fetchLessonData();
      } else {
        throw new Error("Failed to mark lesson as complete");
      }
    } catch (error) {
      console.error("Error marking lesson complete:", error);
      Alert.alert("Error", "Failed to mark lesson as complete");
    } finally {
      setMarkingComplete(false);
    }
  };

  const navigateToLesson = (targetLessonId: string) => {
    navigation.navigate("LessonView", {
      lessonId: targetLessonId,
      courseId,
      slug,
    });
  };

  const renderContent = () => {
    if (!lesson?.content) return null;

    try {
      const content =
        typeof lesson.content === "string"
          ? JSON.parse(lesson.content)
          : lesson.content;

      if (Array.isArray(content)) {
        return content.map((item, index) => {
          if (item.type === "text") {
            return (
              <Text key={index} style={styles.contentText}>
                {item.content}
              </Text>
            );
          } else if (item.type === "html") {
            return (
              <WebView
                key={index}
                source={{ html: item.content }}
                style={styles.webView}
                scrollEnabled={false}
              />
            );
          }
          return null;
        });
      }
    } catch (error) {
      console.error("Error parsing content:", error);
      return (
        <Text style={styles.contentText}>
          {typeof lesson.content === "string"
            ? lesson.content
            : "Content unavailable"}
        </Text>
      );
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Loading lesson...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!lesson) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={48} color="#ff4444" />
          <Text style={styles.errorText}>Lesson not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={styles.backButton}
        >
          <Ionicons name="arrow-back" size={24} color="#007AFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle} numberOfLines={1}>
          {lesson.title}
        </Text>
        <TouchableOpacity
          style={styles.sidebarButton}
          onPress={() => setShowSidebar(!showSidebar)}
        >
          <Ionicons name="list" size={24} color="#007AFF" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Video Player */}
        {lesson.videoUrl && (
          <View style={styles.videoContainer}>
            <VideoView
              ref={videoRef}
              style={styles.video}
              player={useVideoPlayer(lesson.videoUrl, (player) => {
                player.loop = false;
                player.muted = false;
              })}
              allowsFullscreen
              allowsPictureInPicture
            />
          </View>
        )}

        <View style={styles.lessonContent}>
          {/* Lesson Header */}
          <View style={styles.lessonHeader}>
            <Text style={styles.lessonTitle}>{lesson.title}</Text>
            {lesson.description && (
              <Text style={styles.lessonDescription}>{lesson.description}</Text>
            )}
          </View>

          {/* Lesson Content */}
          <View style={styles.contentContainer}>{renderContent()}</View>

          {/* Attachments */}
          {lesson.attachments && lesson.attachments.length > 0 && (
            <View style={styles.attachmentsContainer}>
              <Text style={styles.sectionTitle}>Attachments</Text>
              {lesson.attachments.map((attachment, index) => (
                <TouchableOpacity key={index} style={styles.attachmentItem}>
                  <Ionicons name="document" size={20} color="#007AFF" />
                  <View style={styles.attachmentInfo}>
                    <Text style={styles.attachmentName}>{attachment.name}</Text>
                    {attachment.size && (
                      <Text style={styles.attachmentSize}>
                        {formatFileSize(attachment.size)}
                      </Text>
                    )}
                  </View>
                  <Ionicons name="download" size={20} color="#666" />
                </TouchableOpacity>
              ))}
            </View>
          )}

          {/* Complete Lesson Button */}
          <TouchableOpacity
            style={[
              styles.completeButton,
              isCompleted && styles.completedButton,
              markingComplete && styles.disabledButton,
            ]}
            onPress={markLessonComplete}
            disabled={isCompleted || markingComplete}
          >
            {markingComplete ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <>
                <Ionicons
                  name={isCompleted ? "checkmark-circle" : "checkmark"}
                  size={20}
                  color="#fff"
                />
                <Text style={styles.completeButtonText}>
                  {isCompleted ? "Completed" : "Mark as Complete"}
                </Text>
              </>
            )}
          </TouchableOpacity>

          {/* Navigation */}
          <View style={styles.navigationContainer}>
            {lessonNavigation?.previousLesson && (
              <TouchableOpacity
                style={styles.navButton}
                onPress={() =>
                  navigateToLesson(lessonNavigation.previousLesson!._id)
                }
              >
                <Ionicons name="chevron-back" size={20} color="#007AFF" />
                <Text style={styles.navButtonText}>Previous</Text>
              </TouchableOpacity>
            )}

            {lessonNavigation?.nextLesson && (
              <TouchableOpacity
                style={[styles.navButton, styles.nextButton]}
                onPress={() =>
                  navigateToLesson(lessonNavigation.nextLesson!._id)
                }
              >
                <Text style={styles.navButtonText}>Next</Text>
                <Ionicons name="chevron-forward" size={20} color="#007AFF" />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </ScrollView>

      {/* Sidebar Modal */}
      {showSidebar && (
        <View style={styles.sidebarOverlay}>
          <TouchableOpacity
            style={styles.sidebarBackdrop}
            onPress={() => setShowSidebar(false)}
          />
          <View style={styles.sidebar}>
            <View style={styles.sidebarHeader}>
              <Text style={styles.sidebarTitle}>Course Content</Text>
              <TouchableOpacity onPress={() => setShowSidebar(false)}>
                <Ionicons name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>
            <ScrollView style={styles.sidebarContent}>
              {allLessons.map((module) => (
                <View key={module.moduleId} style={styles.moduleContainer}>
                  <Text style={styles.moduleTitle}>{module.moduleTitle}</Text>
                  {module.lessons.map((moduleLesson) => (
                    <TouchableOpacity
                      key={moduleLesson._id}
                      style={[
                        styles.sidebarLesson,
                        moduleLesson._id === lessonId && styles.currentLesson,
                      ]}
                      onPress={() => {
                        navigateToLesson(moduleLesson._id);
                        setShowSidebar(false);
                      }}
                    >
                      <Ionicons
                        name={
                          moduleLesson.isCompleted
                            ? "checkmark-circle"
                            : "play-circle"
                        }
                        size={16}
                        color={moduleLesson.isCompleted ? "#28a745" : "#666"}
                      />
                      <Text
                        style={[
                          styles.sidebarLessonText,
                          moduleLesson._id === lessonId &&
                            styles.currentLessonText,
                        ]}
                        numberOfLines={2}
                      >
                        {moduleLesson.title}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              ))}
            </ScrollView>
          </View>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8f9fa",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: "#666",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  errorText: {
    fontSize: 18,
    color: "#ff4444",
    marginTop: 16,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e1e5e9",
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1a1a1a",
    flex: 1,
    textAlign: "center",
    marginHorizontal: 8,
  },
  sidebarButton: {
    padding: 8,
  },
  content: {
    flex: 1,
  },
  videoContainer: {
    backgroundColor: "#000",
  },
  video: {
    width: screenWidth,
    height: screenWidth * 0.5625, // 16:9 aspect ratio
  },
  lessonContent: {
    padding: 20,
  },
  lessonHeader: {
    marginBottom: 20,
  },
  lessonTitle: {
    fontSize: 24,
    fontWeight: "700",
    color: "#1a1a1a",
    marginBottom: 8,
  },
  lessonDescription: {
    fontSize: 16,
    color: "#666",
    lineHeight: 24,
  },
  contentContainer: {
    marginBottom: 24,
  },
  contentText: {
    fontSize: 16,
    color: "#333",
    lineHeight: 24,
    marginBottom: 16,
  },
  webView: {
    height: 200,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1a1a1a",
    marginBottom: 12,
  },
  attachmentsContainer: {
    marginBottom: 24,
  },
  attachmentItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    backgroundColor: "#f8f9fa",
    borderRadius: 8,
    marginBottom: 8,
  },
  attachmentInfo: {
    flex: 1,
    marginLeft: 12,
  },
  attachmentName: {
    fontSize: 14,
    fontWeight: "500",
    color: "#1a1a1a",
  },
  attachmentSize: {
    fontSize: 12,
    color: "#666",
    marginTop: 2,
  },
  completeButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#007AFF",
    borderRadius: 8,
    paddingVertical: 16,
    marginBottom: 24,
    gap: 8,
  },
  completedButton: {
    backgroundColor: "#28a745",
  },
  disabledButton: {
    backgroundColor: "#ccc",
  },
  completeButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  navigationContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  navButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: "#007AFF",
    borderRadius: 8,
    gap: 8,
  },
  nextButton: {
    marginLeft: "auto",
  },
  navButtonText: {
    color: "#007AFF",
    fontSize: 16,
    fontWeight: "500",
  },
  sidebarOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    flexDirection: "row",
  },
  sidebarBackdrop: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  sidebar: {
    width: screenWidth * 0.8,
    backgroundColor: "#fff",
    maxWidth: 320,
  },
  sidebarHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#e1e5e9",
  },
  sidebarTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1a1a1a",
  },
  sidebarContent: {
    flex: 1,
    padding: 16,
  },
  moduleContainer: {
    marginBottom: 20,
  },
  moduleTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1a1a1a",
    marginBottom: 8,
  },
  sidebarLesson: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    borderRadius: 8,
    marginBottom: 4,
    gap: 8,
  },
  currentLesson: {
    backgroundColor: "#f0f8ff",
  },
  sidebarLessonText: {
    fontSize: 14,
    color: "#666",
    flex: 1,
  },
  currentLessonText: {
    color: "#007AFF",
    fontWeight: "500",
  },
});

export default LessonViewScreen;
