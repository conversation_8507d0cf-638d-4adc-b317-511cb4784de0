import io, { Socket } from "socket.io-client";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { store } from "../store";
import {
  addMessage,
  addTypingUser,
  removeTypingUser,
  updateUserOnlineStatus,
} from "../store/slices/chatSlice";
import { addNotification } from "../store/slices/notificationSlice";

class WebSocketService {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private isConnecting = false;

  async connect() {
    if (this.socket?.connected || this.isConnecting) {
      return;
    }

    this.isConnecting = true;

    try {
      const token = await AsyncStorage.getItem("auth_token");
      if (!token) {
        console.log("No auth token found, skipping WebSocket connection");
        this.isConnecting = false;
        return;
      }

      // Check if WebSocket is disabled
      if (process.env.ENABLE_WEBSOCKET === "false") {
        console.log("WebSocket disabled in configuration");
        this.isConnecting = false;
        return;
      }

      // Get WebSocket URL from environment
      const socketUrl = __DEV__
        ? process.env.WEBSOCKET_URL_DEV || "http://localhost:3000"
        : process.env.WEBSOCKET_URL_PROD || "https://thetribelab.com";

      this.socket = io(socketUrl, {
        auth: {
          token,
        },
        transports: ["websocket"],
        autoConnect: true,
        reconnection: true,
        reconnectionAttempts: this.maxReconnectAttempts,
        reconnectionDelay: this.reconnectDelay,
        timeout: 20000,
      });

      this.setupEventListeners();
      this.isConnecting = false;
    } catch (error) {
      console.error("WebSocket connection error:", error);
      this.isConnecting = false;
    }
  }

  private setupEventListeners() {
    if (!this.socket) return;

    // Connection events
    this.socket.on("connect", () => {
      console.log("WebSocket connected");
      this.reconnectAttempts = 0;

      // Join user's personal room for notifications
      const state = store.getState();
      const userId = state.auth.user?.id;
      if (userId) {
        this.socket?.emit("join_user_room", userId);
      }
    });

    this.socket.on("disconnect", (reason) => {
      console.log("WebSocket disconnected:", reason);
    });

    this.socket.on("connect_error", (error) => {
      console.error("WebSocket connection error:", error);
      this.reconnectAttempts++;

      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        console.log("Max reconnection attempts reached");
        this.disconnect();
      }
    });

    // Chat events
    this.socket.on("new_message", (message) => {
      console.log("New message received:", message);
      store.dispatch(addMessage(message));
    });

    this.socket.on("user_typing", (data) => {
      console.log("User typing:", data);
      store.dispatch(
        addTypingUser({
          userId: data.userId,
          userName: data.userName,
          chatId: data.chatId,
        })
      );

      // Remove typing indicator after 3 seconds
      setTimeout(() => {
        store.dispatch(
          removeTypingUser({
            userId: data.userId,
            chatId: data.chatId,
          })
        );
      }, 3000);
    });

    this.socket.on("user_stopped_typing", (data) => {
      console.log("User stopped typing:", data);
      store.dispatch(
        removeTypingUser({
          userId: data.userId,
          chatId: data.chatId,
        })
      );
    });

    // User presence events
    this.socket.on("user_online", (data) => {
      console.log("User came online:", data);
      store.dispatch(
        updateUserOnlineStatus({
          userId: data.userId,
          isOnline: true,
        })
      );
    });

    this.socket.on("user_offline", (data) => {
      console.log("User went offline:", data);
      store.dispatch(
        updateUserOnlineStatus({
          userId: data.userId,
          isOnline: false,
        })
      );
    });

    // Notification events
    this.socket.on("new_notification", (notification) => {
      console.log("New notification received:", notification);
      store.dispatch(addNotification(notification));
    });

    // Community events
    this.socket.on("community_update", (data) => {
      console.log("Community update:", data);
      // Handle community updates (new posts, member joins, etc.)
    });

    // Course events
    this.socket.on("course_update", (data) => {
      console.log("Course update:", data);
      // Handle course updates (new lessons, progress updates, etc.)
    });

    // System events
    this.socket.on("system_announcement", (data) => {
      console.log("System announcement:", data);
      // Handle system announcements
    });
  }

  // Chat methods
  joinChatRoom(chatId: string) {
    if (this.socket?.connected) {
      this.socket.emit("join_chat", chatId);
    } else if (__DEV__) {
      console.log(`Development mode: Would join chat room ${chatId}`);
    }
  }

  leaveChatRoom(chatId: string) {
    if (this.socket?.connected) {
      this.socket.emit("leave_chat", chatId);
    } else if (__DEV__) {
      console.log(`Development mode: Would leave chat room ${chatId}`);
    }
  }

  sendMessage(chatId: string, message: string, type: string = "text") {
    if (this.socket?.connected) {
      this.socket.emit("send_message", {
        chatId,
        message,
        type,
        timestamp: new Date().toISOString(),
      });
    } else if (__DEV__) {
      console.log(
        `Development mode: Would send message to ${chatId}:`,
        message
      );
    }
  }

  startTyping(chatId: string) {
    if (this.socket?.connected) {
      this.socket.emit("start_typing", { chatId });
    }
  }

  stopTyping(chatId: string) {
    if (this.socket?.connected) {
      this.socket.emit("stop_typing", { chatId });
    }
  }

  // Community methods
  joinCommunityRoom(communityId: string) {
    if (this.socket?.connected) {
      this.socket.emit("join_community", communityId);
    }
  }

  leaveCommunityRoom(communityId: string) {
    if (this.socket?.connected) {
      this.socket.emit("leave_community", communityId);
    }
  }

  // Course methods
  joinCourseRoom(courseId: string) {
    if (this.socket?.connected) {
      this.socket.emit("join_course", courseId);
    }
  }

  leaveCourseRoom(courseId: string) {
    if (this.socket?.connected) {
      this.socket.emit("leave_course", courseId);
    }
  }

  // Notification methods
  markNotificationAsRead(notificationId: string) {
    if (this.socket?.connected) {
      this.socket.emit("mark_notification_read", notificationId);
    }
  }

  // General methods
  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.reconnectAttempts = 0;
    this.isConnecting = false;
  }

  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  // Custom event emitter
  emit(event: string, data: any) {
    if (this.socket?.connected) {
      this.socket.emit(event, data);
    }
  }

  // Custom event listener
  on(event: string, callback: (data: any) => void) {
    if (this.socket) {
      this.socket.on(event, callback);
    }
  }

  // Remove event listener
  off(event: string, callback?: (data: any) => void) {
    if (this.socket) {
      this.socket.off(event, callback);
    }
  }
}

// Create singleton instance
const websocketService = new WebSocketService();

export default websocketService;
