import * as ImagePicker from 'expo-image-picker';
import * as Crypto from 'expo-crypto';

// R2 Configuration
const R2_PUBLIC_URL = process.env.EXPO_PUBLIC_R2_PUBLIC_URL;

/**
 * Image picker options for different use cases
 */
export const ImagePickerOptions = {
  profile: {
    mediaTypes: ImagePicker.MediaTypeOptions.Images,
    allowsEditing: true,
    aspect: [1, 1] as [number, number],
    quality: 0.8,
    allowsMultipleSelection: false,
  },
  banner: {
    mediaTypes: ImagePicker.MediaTypeOptions.Images,
    allowsEditing: true,
    aspect: [16, 9] as [number, number],
    quality: 0.9,
    allowsMultipleSelection: false,
  },
  general: {
    mediaTypes: ImagePicker.MediaTypeOptions.Images,
    allowsEditing: true,
    quality: 0.8,
    allowsMultipleSelection: false,
  },
  multiple: {
    mediaTypes: ImagePicker.MediaTypeOptions.Images,
    allowsEditing: false,
    quality: 0.8,
    allowsMultipleSelection: true,
    selectionLimit: 5,
  }
};

/**
 * Request camera and media library permissions
 */
export const requestImagePermissions = async (): Promise<boolean> => {
  try {
    // Request camera permission
    const cameraPermission = await ImagePicker.requestCameraPermissionsAsync();
    
    // Request media library permission
    const mediaPermission = await ImagePicker.requestMediaLibraryPermissionsAsync();
    
    const hasPermissions = cameraPermission.status === 'granted' && 
                          mediaPermission.status === 'granted';
    
    if (!hasPermissions) {
      console.warn('Camera or media library permissions not granted');
    }
    
    return hasPermissions;
  } catch (error) {
    console.error('Error requesting image permissions:', error);
    return false;
  }
};

/**
 * Pick image from camera
 */
export const pickImageFromCamera = async (options = ImagePickerOptions.general): Promise<ImagePicker.ImagePickerResult> => {
  try {
    const hasPermission = await requestImagePermissions();
    if (!hasPermission) {
      throw new Error('Camera permission not granted');
    }

    const result = await ImagePicker.launchCameraAsync(options);
    return result;
  } catch (error) {
    console.error('Error picking image from camera:', error);
    throw error;
  }
};

/**
 * Pick image from gallery
 */
export const pickImageFromGallery = async (options = ImagePickerOptions.general): Promise<ImagePicker.ImagePickerResult> => {
  try {
    const hasPermission = await requestImagePermissions();
    if (!hasPermission) {
      throw new Error('Media library permission not granted');
    }

    const result = await ImagePicker.launchImageLibraryAsync(options);
    return result;
  } catch (error) {
    console.error('Error picking image from gallery:', error);
    throw error;
  }
};

/**
 * Show image picker action sheet
 */
export const showImagePicker = async (options = ImagePickerOptions.general): Promise<ImagePicker.ImagePickerResult | null> => {
  return new Promise((resolve) => {
    // For now, we'll just use gallery picker
    // In a real implementation, you might want to use ActionSheet or Alert
    pickImageFromGallery(options)
      .then(resolve)
      .catch((error) => {
        console.error('Image picker error:', error);
        resolve(null);
      });
  });
};

/**
 * Convert image URI to blob for upload
 */
export const uriToBlob = async (uri: string): Promise<Blob> => {
  try {
    const response = await fetch(uri);
    const blob = await response.blob();
    return blob;
  } catch (error) {
    console.error('Error converting URI to blob:', error);
    throw new Error('Failed to process image');
  }
};

/**
 * Upload image to R2 via backend API
 */
export const uploadImageToR2 = async (
  imageUri: string, 
  uploadType: 'profile' | 'banner' | 'thumbnail' | 'general' = 'general',
  additionalData?: Record<string, any>
): Promise<{ url: string; fileName: string; thumbnail?: { url: string; fileName: string } }> => {
  try {
    // Convert image URI to blob
    const blob = await uriToBlob(imageUri);
    
    // Create FormData
    const formData = new FormData();
    formData.append('image', blob as any, 'image.jpg');
    formData.append('uploadType', uploadType);
    
    if (additionalData) {
      Object.keys(additionalData).forEach(key => {
        formData.append(key, additionalData[key]);
      });
    }

    // Get auth token
    const token = await getAuthToken();
    
    // Upload to backend
    const response = await fetch(`${process.env.API_BASE_URL_DEV || 'http://localhost:4000'}/api/upload/image`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        // Don't set Content-Type, let the browser set it with boundary for FormData
      },
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `Upload failed with status ${response.status}`);
    }

    const result = await response.json();
    return result.data;

  } catch (error) {
    console.error('R2 upload error:', error);
    throw error;
  }
};

/**
 * Get optimized R2 image URL with transformations
 */
export const getOptimizedImageUrl = (
  fileName: string, 
  options?: {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'webp' | 'jpeg' | 'png';
  }
): string => {
  if (!fileName || !R2_PUBLIC_URL) {
    return '';
  }

  // If it's already a full URL, return as is
  if (fileName.startsWith('http')) {
    return fileName;
  }

  // Build base URL
  let url = `${R2_PUBLIC_URL}/${fileName}`;

  // Add transformations if supported by your R2 setup
  if (options) {
    const params = new URLSearchParams();
    
    if (options.width) params.append('w', options.width.toString());
    if (options.height) params.append('h', options.height.toString());
    if (options.quality) params.append('q', options.quality.toString());
    if (options.format) params.append('f', options.format);

    const queryString = params.toString();
    if (queryString) {
      url += `?${queryString}`;
    }
  }

  return url;
};

/**
 * Get thumbnail URL for an image
 */
export const getThumbnailUrl = (fileName: string): string => {
  if (!fileName || !R2_PUBLIC_URL) {
    return '';
  }

  // If it's already a full URL, try to get thumbnail version
  if (fileName.startsWith('http')) {
    return fileName;
  }

  // Generate thumbnail path
  const pathParts = fileName.split('/');
  const filename = pathParts.pop();
  const directory = pathParts.join('/');
  
  const thumbnailPath = directory 
    ? `${directory}/thumbnails/${filename}`
    : `thumbnails/${filename}`;

  return `${R2_PUBLIC_URL}/${thumbnailPath}`;
};

/**
 * Validate image before upload
 */
export const validateImage = (imageResult: ImagePicker.ImagePickerResult): boolean => {
  if (imageResult.canceled || !imageResult.assets || imageResult.assets.length === 0) {
    return false;
  }

  const asset = imageResult.assets[0];
  
  // Check file size (10MB limit)
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (asset.fileSize && asset.fileSize > maxSize) {
    throw new Error('Image too large. Maximum size is 10MB.');
  }

  // Check dimensions (optional)
  const maxDimension = 4096;
  if (asset.width > maxDimension || asset.height > maxDimension) {
    throw new Error(`Image dimensions too large. Maximum is ${maxDimension}x${maxDimension}px.`);
  }

  return true;
};

/**
 * Generate cache key for image
 */
export const generateImageCacheKey = (url: string): string => {
  return Crypto.digestStringAsync(Crypto.CryptoDigestAlgorithm.MD5, url);
};

/**
 * Helper to get auth token (you'll need to implement this based on your auth system)
 */
const getAuthToken = async (): Promise<string> => {
  // This should get the token from your auth storage
  // For now, returning empty string - you'll need to implement this
  try {
    const AsyncStorage = require('@react-native-async-storage/async-storage').default;
    const token = await AsyncStorage.getItem('auth_token');
    return token || '';
  } catch (error) {
    console.error('Error getting auth token:', error);
    return '';
  }
};

/**
 * Image cache utilities
 */
export const ImageCache = {
  /**
   * Preload images for better performance
   */
  preloadImages: async (urls: string[]): Promise<void> => {
    try {
      const promises = urls.map(url => {
        return new Promise<void>((resolve) => {
          const image = new Image();
          image.onload = () => resolve();
          image.onerror = () => resolve(); // Don't fail on individual image errors
          image.src = url;
        });
      });

      await Promise.all(promises);
    } catch (error) {
      console.warn('Image preloading failed:', error);
    }
  },

  /**
   * Clear image cache (if needed)
   */
  clearCache: async (): Promise<void> => {
    // Implementation depends on your caching strategy
    console.log('Image cache cleared');
  }
};
