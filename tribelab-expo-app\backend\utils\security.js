import crypto from 'crypto';
import bcrypt from 'bcryptjs';

// Security configuration
const ENCRYPTION_SECRET = process.env.ENCRYPTION_SECRET || '';
const RECAPTCHA_SECRET_KEY = process.env.RECAPTCHA_SECRET_KEY || '';

/**
 * Encrypt sensitive data
 */
export const encryptData = (data) => {
  try {
    if (!ENCRYPTION_SECRET) {
      console.warn('⚠️ Encryption secret not configured');
      return data;
    }

    const algorithm = 'aes-256-gcm';
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(algorithm, ENCRYPTION_SECRET);
    
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    return `${iv.toString('hex')}:${encrypted}`;
  } catch (error) {
    console.error('Encryption error:', error);
    return data;
  }
};

/**
 * Decrypt sensitive data
 */
export const decryptData = (encryptedData) => {
  try {
    if (!ENCRYPTION_SECRET || !encryptedData.includes(':')) {
      return encryptedData;
    }

    const algorithm = 'aes-256-gcm';
    const [ivHex, encrypted] = encryptedData.split(':');
    const iv = Buffer.from(ivHex, 'hex');
    const decipher = crypto.createDecipher(algorithm, ENCRYPTION_SECRET);
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  } catch (error) {
    console.error('Decryption error:', error);
    return encryptedData;
  }
};

/**
 * Hash password with bcrypt
 */
export const hashPassword = async (password) => {
  try {
    const saltRounds = 12;
    return await bcrypt.hash(password, saltRounds);
  } catch (error) {
    console.error('Password hashing error:', error);
    throw new Error('Failed to hash password');
  }
};

/**
 * Verify password with bcrypt
 */
export const verifyPassword = async (password, hashedPassword) => {
  try {
    return await bcrypt.compare(password, hashedPassword);
  } catch (error) {
    console.error('Password verification error:', error);
    return false;
  }
};

/**
 * Generate secure random token
 */
export const generateSecureToken = (length = 32) => {
  return crypto.randomBytes(length).toString('hex');
};

/**
 * Generate API key
 */
export const generateApiKey = () => {
  const prefix = 'tl_';
  const randomPart = crypto.randomBytes(24).toString('hex');
  return `${prefix}${randomPart}`;
};

/**
 * Create HMAC signature
 */
export const createSignature = (data, secret = ENCRYPTION_SECRET) => {
  return crypto
    .createHmac('sha256', secret)
    .update(data)
    .digest('hex');
};

/**
 * Verify HMAC signature
 */
export const verifySignature = (data, signature, secret = ENCRYPTION_SECRET) => {
  const expectedSignature = createSignature(data, secret);
  return crypto.timingSafeEqual(
    Buffer.from(signature, 'hex'),
    Buffer.from(expectedSignature, 'hex')
  );
};

/**
 * Sanitize user input
 */
export const sanitizeInput = (input) => {
  if (typeof input !== 'string') return input;
  
  return input
    .replace(/[<>]/g, '') // Remove < and >
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .replace(/script/gi, '') // Remove script tags
    .trim();
};

/**
 * Validate email format
 */
export const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validate password strength
 */
export const validatePassword = (password) => {
  const errors = [];
  let score = 0;

  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  } else {
    score += 1;
  }

  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  } else {
    score += 1;
  }

  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  } else {
    score += 1;
  }

  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  } else {
    score += 1;
  }

  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character');
  } else {
    score += 1;
  }

  let strength = 'weak';
  if (score >= 4) strength = 'strong';
  else if (score >= 3) strength = 'medium';

  return {
    isValid: errors.length === 0,
    errors,
    strength,
  };
};

/**
 * Rate limiting class
 */
class RateLimiter {
  constructor(maxAttempts = 5, windowMs = 15 * 60 * 1000) {
    this.attempts = new Map();
    this.maxAttempts = maxAttempts;
    this.windowMs = windowMs;
  }

  isAllowed(identifier) {
    const now = Date.now();
    const attempts = this.attempts.get(identifier) || [];
    
    // Remove old attempts outside the window
    const validAttempts = attempts.filter(time => now - time < this.windowMs);
    
    if (validAttempts.length >= this.maxAttempts) {
      return false;
    }

    // Add current attempt
    validAttempts.push(now);
    this.attempts.set(identifier, validAttempts);
    
    return true;
  }

  getRemainingTime(identifier) {
    const attempts = this.attempts.get(identifier) || [];
    if (attempts.length === 0) return 0;
    
    const oldestAttempt = Math.min(...attempts);
    const remainingTime = this.windowMs - (Date.now() - oldestAttempt);
    
    return Math.max(0, remainingTime);
  }

  reset(identifier) {
    this.attempts.delete(identifier);
  }
}

// Export rate limiter instances
export const loginRateLimiter = new RateLimiter(5, 15 * 60 * 1000); // 5 attempts per 15 minutes
export const apiRateLimiter = new RateLimiter(100, 60 * 1000); // 100 requests per minute

/**
 * Verify reCAPTCHA token
 */
export const verifyRecaptcha = async (token) => {
  try {
    if (!RECAPTCHA_SECRET_KEY) {
      console.warn('⚠️ reCAPTCHA secret key not configured');
      return true; // Skip validation if not configured
    }

    const response = await fetch('https://www.google.com/recaptcha/api/siteverify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: `secret=${RECAPTCHA_SECRET_KEY}&response=${token}`,
    });

    const data = await response.json();
    return data.success;
  } catch (error) {
    console.error('reCAPTCHA verification error:', error);
    return false;
  }
};

/**
 * Generate JWT payload with security claims
 */
export const createJwtPayload = (user, additionalClaims = {}) => {
  return {
    id: user._id || user.id,
    email: user.email,
    role: user.role || 'user',
    iat: Math.floor(Date.now() / 1000),
    ...additionalClaims,
  };
};

/**
 * Security middleware for sensitive operations
 */
export const requireSecurityCheck = (req, res, next) => {
  const deviceFingerprint = req.headers['x-device-fingerprint'];
  const clientVersion = req.headers['x-client-version'];
  
  if (!deviceFingerprint) {
    return res.status(400).json({
      success: false,
      error: 'Security check failed: Missing device fingerprint',
    });
  }

  // Add security context to request
  req.security = {
    deviceFingerprint,
    clientVersion,
    timestamp: Date.now(),
  };

  next();
};

/**
 * Input validation middleware
 */
export const validateInput = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body);
    
    if (error) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: error.details.map(detail => detail.message),
      });
    }

    // Sanitize validated input
    req.body = sanitizeObject(value);
    next();
  };
};

/**
 * Sanitize object recursively
 */
const sanitizeObject = (obj) => {
  if (typeof obj === 'string') {
    return sanitizeInput(obj);
  }
  
  if (Array.isArray(obj)) {
    return obj.map(sanitizeObject);
  }
  
  if (obj && typeof obj === 'object') {
    const sanitized = {};
    for (const [key, value] of Object.entries(obj)) {
      sanitized[key] = sanitizeObject(value);
    }
    return sanitized;
  }
  
  return obj;
};

/**
 * Security audit logger
 */
export const auditLog = (action, userId, details = {}) => {
  const logEntry = {
    timestamp: new Date().toISOString(),
    action,
    userId,
    details,
    ip: details.ip || 'unknown',
    userAgent: details.userAgent || 'unknown',
  };

  // In production, send to logging service
  console.log('🔒 Security Audit:', JSON.stringify(logEntry));
};

/**
 * Security utilities object
 */
export const Security = {
  encrypt: encryptData,
  decrypt: decryptData,
  hashPassword,
  verifyPassword,
  generateToken: generateSecureToken,
  generateApiKey,
  createSignature,
  verifySignature,
  sanitize: sanitizeInput,
  validateEmail,
  validatePassword,
  verifyRecaptcha,
  createJwtPayload,
  requireSecurityCheck,
  validateInput,
  auditLog,
  rateLimiters: {
    login: loginRateLimiter,
    api: apiRateLimiter,
  },
};
