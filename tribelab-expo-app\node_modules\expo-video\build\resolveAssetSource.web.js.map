{"version": 3, "file": "resolveAssetSource.web.js", "sourceRoot": "", "sources": ["../src/resolveAssetSource.web.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,wCAAwC,CAAC;AAEtE,uGAAuG;AACvG,MAAM,CAAC,OAAO,UAAU,kBAAkB,CAAC,OAAe;IACxD,MAAM,KAAK,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;IACpC,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,OAAO,IAAI,CAAC;IACd,CAAC;IACD,MAAM,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;IACjD,MAAM,SAAS,GAAG,OAAO;QACvB,CAAC,CAAC,KAAK,CAAC,kBAAkB,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,GAAG,IAAI;QACpD,CAAC,CAAC,KAAK,CAAC,kBAAkB,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;IAE/E,wGAAwG;IACxG,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,SAAS,EAAE,kBAAkB,CAAC,CAAC;IACvD,OAAO,EAAE,GAAG,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;AACjE,CAAC", "sourcesContent": ["import { getAssetByID } from '@react-native/assets-registry/registry';\n\n// Minimal `resolveAssetSource` implementation for video on web, based on the version from `expo-asset`\nexport default function resolveAssetSource(assetId: number): { uri: string } | null {\n  const asset = getAssetByID(assetId);\n  if (!asset) {\n    return null;\n  }\n  const type = !asset.type ? '' : `.${asset.type}`;\n  const assetPath = __DEV__\n    ? asset.httpServerLocation + '/' + asset.name + type\n    : asset.httpServerLocation.replace(/\\.\\.\\//g, '_') + '/' + asset.name + type;\n\n  // The base has to have a valid syntax but doesn't matter - it's removed below as we use a relative path\n  const fromUrl = new URL(assetPath, 'https://expo.dev');\n  return { uri: fromUrl.toString().replace(fromUrl.origin, '') };\n}\n"]}