{"version": 3, "file": "VideoPlayer.js", "sourceRoot": "", "sources": ["../src/VideoPlayer.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,wBAAwB,EAAE,MAAM,mBAAmB,CAAC;AAE7D,OAAO,iBAAiB,MAAM,qBAAqB,CAAC;AAEpD,OAAO,kBAAkB,MAAM,sBAAsB,CAAC;AAEtD,4HAA4H;AAC5H,MAAM,OAAO,GAAG,iBAAiB,CAAC,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC;AAChE,iBAAiB,CAAC,WAAW,CAAC,SAAS,CAAC,OAAO,GAAG,UAChD,MAAmB,EACnB,iBAA0B,KAAK;IAE/B,IAAI,CAAC,cAAc,EAAE,CAAC;QACpB,OAAO,CAAC,IAAI,CACV,mNAAmN,CACpN,CAAC;IACJ,CAAC;IAED,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC;AACjD,CAAC,CAAC;AAEF,MAAM,YAAY,GAAG,iBAAiB,CAAC,WAAW,CAAC,SAAS,CAAC,YAAY,CAAC;AAC1E,iBAAiB,CAAC,WAAW,CAAC,SAAS,CAAC,YAAY,GAAG,UAAU,MAAmB;IAClF,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC;AACtD,CAAC,CAAC;AACF;;;;;GAKG;AACH,MAAM,UAAU,iBAAiB,CAAC,MAAmB;IACnD,OAAO,IAAI,iBAAiB,CAAC,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC;AAChE,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,cAAc,CAC5B,MAAmB,EACnB,KAAqC;IAErC,MAAM,YAAY,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;IAEzC,OAAO,wBAAwB,CAAC,GAAG,EAAE;QACnC,MAAM,MAAM,GAAG,IAAI,iBAAiB,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;QAC/D,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC;QAChB,OAAO,MAAM,CAAC;IAChB,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;AACrC,CAAC;AAED,SAAS,WAAW,CAAC,MAAmB;IACtC,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;QAC/B,4EAA4E;QAC5E,OAAO,EAAE,GAAG,EAAE,kBAAkB,CAAC,MAAM,CAAE,CAAC,GAAG,EAAE,CAAC;IAClD,CAAC;SAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;QACtC,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC;IACzB,CAAC;IAED,IAAI,OAAO,MAAM,EAAE,OAAO,KAAK,QAAQ,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;QACvD,4EAA4E;QAC5E,OAAO,EAAE,GAAG,MAAM,EAAE,GAAG,EAAE,kBAAkB,CAAC,MAAM,CAAC,OAAO,CAAE,CAAC,GAAG,EAAE,CAAC;IACrE,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC", "sourcesContent": ["import { useReleasingSharedObject } from 'expo-modules-core';\n\nimport NativeVideoModule from './NativeVideoModule';\nimport { VideoSource, VideoPlayer } from './VideoPlayer.types';\nimport resolveAssetSource from './resolveAssetSource';\n\n// TODO: Temporary solution until we develop a way of overriding prototypes that won't break the lazy loading of the module.\nconst replace = NativeVideoModule.VideoPlayer.prototype.replace;\nNativeVideoModule.VideoPlayer.prototype.replace = function (\n  source: VideoSource,\n  disableWarning: boolean = false\n) {\n  if (!disableWarning) {\n    console.warn(\n      'On iOS `VideoPlayer.replace` loads the asset data synchronously on the main thread, which can lead to UI freezes and will be deprecated in a future release. Switch to `replaceAsync` for better user experience.'\n    );\n  }\n\n  return replace.call(this, parseSource(source));\n};\n\nconst replaceAsync = NativeVideoModule.VideoPlayer.prototype.replaceAsync;\nNativeVideoModule.VideoPlayer.prototype.replaceAsync = function (source: VideoSource) {\n  return replaceAsync.call(this, parseSource(source));\n};\n/**\n * Creates a direct instance of `VideoPlayer` that doesn't release automatically.\n *\n * > **info** For most use cases you should use the [`useVideoPlayer`](#usevideoplayer) hook instead. See the [Using the VideoPlayer Directly](#using-the-videoplayer-directly) section for more details.\n * @param source\n */\nexport function createVideoPlayer(source: VideoSource): VideoPlayer {\n  return new NativeVideoModule.VideoPlayer(parseSource(source));\n}\n\n/**\n * Creates a `VideoPlayer`, which will be automatically cleaned up when the component is unmounted.\n * @param source - A video source that is used to initialize the player.\n * @param setup - A function that allows setting up the player. It will run after the player is created.\n */\nexport function useVideoPlayer(\n  source: VideoSource,\n  setup?: (player: VideoPlayer) => void\n): VideoPlayer {\n  const parsedSource = parseSource(source);\n\n  return useReleasingSharedObject(() => {\n    const player = new NativeVideoModule.VideoPlayer(parsedSource);\n    setup?.(player);\n    return player;\n  }, [JSON.stringify(parsedSource)]);\n}\n\nfunction parseSource(source: VideoSource): VideoSource {\n  if (typeof source === 'number') {\n    // TODO(@kitten): This seems to not handle the `null` case. Is this correct?\n    return { uri: resolveAssetSource(source)!.uri };\n  } else if (typeof source === 'string') {\n    return { uri: source };\n  }\n\n  if (typeof source?.assetId === 'number' && !source.uri) {\n    // TODO(@kitten): This seems to not handle the `null` case. Is this correct?\n    return { ...source, uri: resolveAssetSource(source.assetId)!.uri };\n  }\n  return source;\n}\n"]}