{"version": 3, "file": "./lib/fxvalidator.min.js", "mappings": "CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAsB,aAAID,IAE1BD,EAAmB,aAAIC,GACxB,CATD,CASGK,MAAM,I,mBCRT,IAAIC,EAAsB,CCA1BA,EAAwB,CAACL,EAASM,KACjC,IAAI,IAAIC,KAAOD,EACXD,EAAoBG,EAAEF,EAAYC,KAASF,EAAoBG,EAAER,EAASO,IAC5EE,OAAOC,eAAeV,EAASO,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,IAE1E,ECNDF,EAAwB,CAACQ,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,GCClFT,EAAyBL,IACH,oBAAXkB,QAA0BA,OAAOC,aAC1CV,OAAOC,eAAeV,EAASkB,OAAOC,YAAa,CAAEC,MAAO,WAE7DX,OAAOC,eAAeV,EAAS,aAAc,CAAEoB,OAAO,GAAO,G,oCCH9D,IAAMC,EAAgB,gLAGhBC,EAAY,IAAIC,OAAO,KADGF,EAAgB,KAD/BA,EAEY,mDAkBhBG,EAAS,SAASC,GAE7B,QAAQ,MADMH,EAAUI,KAAKD,GAE/B,ECtBME,EAAiB,CACrBC,wBAAwB,EACxBC,aAAc,IAIT,SAASC,EAASC,EAASC,GAChCA,EAAUvB,OAAOwB,OAAO,CAAC,EAAGN,EAAgBK,GAK5C,IAAME,EAAO,GACTC,GAAW,EAGXC,GAAc,EAEC,WAAfL,EAAQ,KAEVA,EAAUA,EAAQM,OAAO,IAG3B,IAAK,IAAIC,EAAI,EAAGA,EAAIP,EAAQQ,OAAQD,IAElC,GAAmB,MAAfP,EAAQO,IAA+B,MAAjBP,EAAQO,EAAE,IAGlC,IADAA,EAAIE,EAAOT,EADXO,GAAG,IAEGG,IAAK,OAAOH,MACd,IAAmB,MAAfP,EAAQO,GA0IX,CACL,GAAKI,EAAaX,EAAQO,IACxB,SAEF,OAAOK,EAAe,cAAe,SAASZ,EAAQO,GAAG,qBAAsBM,EAAyBb,EAASO,GACnH,CA5IE,IAAIO,EAAcP,EAGlB,GAAmB,MAAfP,IAFJO,GAEwB,CACtBA,EAAIQ,EAAoBf,EAASO,GACjC,QACF,CACE,IAAIS,GAAa,EACE,MAAfhB,EAAQO,KAEVS,GAAa,EACbT,KAIF,IADA,IAAIU,EAAU,GACPV,EAAIP,EAAQQ,QACF,MAAfR,EAAQO,IACO,MAAfP,EAAQO,IACO,OAAfP,EAAQO,IACO,OAAfP,EAAQO,IACO,OAAfP,EAAQO,GAAaA,IAErBU,GAAWjB,EAAQO,GAWrB,GANoC,OAHpCU,EAAUA,EAAQC,QAGND,EAAQT,OAAS,KAE3BS,EAAUA,EAAQE,UAAU,EAAGF,EAAQT,OAAS,GAEhDD,MAoVDd,EAlVoBwB,GAOnB,OAAOL,EAAe,aALQ,IAA1BK,EAAQC,OAAOV,OACX,2BAEA,QAAQS,EAAQ,wBAEiBJ,EAAyBb,EAASO,IAG7E,IAAMa,EAASC,EAAiBrB,EAASO,GACzC,IAAe,IAAXa,EACF,OAAOR,EAAe,cAAe,mBAAmBK,EAAQ,qBAAsBJ,EAAyBb,EAASO,IAE1H,IAAIe,EAAUF,EAAO/B,MAGrB,GAFAkB,EAAIa,EAAOG,MAEyB,MAAhCD,EAAQA,EAAQd,OAAS,GAAY,CAEvC,IAAMgB,EAAejB,EAAIe,EAAQd,OAE3BiB,EAAUC,EADhBJ,EAAUA,EAAQH,UAAU,EAAGG,EAAQd,OAAS,GACCP,GACjD,IAAgB,IAAZwB,EAOF,OAAOb,EAAea,EAAQf,IAAIiB,KAAMF,EAAQf,IAAIkB,IAAKf,EAAyBb,EAASwB,EAAeC,EAAQf,IAAImB,OANtHzB,GAAW,CAQf,MAAO,GAAIY,EAAY,CACrB,IAAKI,EAAOU,UACV,OAAOlB,EAAe,aAAc,gBAAgBK,EAAQ,iCAAkCJ,EAAyBb,EAASO,IAC3H,GAAIe,EAAQJ,OAAOV,OAAS,EACjC,OAAOI,EAAe,aAAc,gBAAgBK,EAAQ,+CAAgDJ,EAAyBb,EAASc,IACzI,GAAoB,IAAhBX,EAAKK,OACd,OAAOI,EAAe,aAAc,gBAAgBK,EAAQ,yBAA0BJ,EAAyBb,EAASc,IAExH,IAAMiB,EAAM5B,EAAK6B,MACjB,GAAIf,IAAYc,EAAId,QAAS,CAC3B,IAAIgB,EAAUpB,EAAyBb,EAAS+B,EAAIjB,aACpD,OAAOF,EAAe,aACpB,yBAAyBmB,EAAId,QAAQ,qBAAqBgB,EAAQJ,KAAK,SAASI,EAAQC,IAAI,6BAA6BjB,EAAQ,KACjIJ,EAAyBb,EAASc,GACtC,CAGmB,GAAfX,EAAKK,SACPH,GAAc,EAGpB,KAAO,CACL,IAAMoB,EAAUC,EAAwBJ,EAASrB,GACjD,IAAgB,IAAZwB,EAIF,OAAOb,EAAea,EAAQf,IAAIiB,KAAMF,EAAQf,IAAIkB,IAAKf,EAAyBb,EAASO,EAAIe,EAAQd,OAASiB,EAAQf,IAAImB,OAI9H,IAAoB,IAAhBxB,EACF,OAAOO,EAAe,aAAc,sCAAuCC,EAAyBb,EAASO,KAC1D,IAA3CN,EAAQH,aAAaqC,QAAQlB,IAGrCd,EAAKiC,KAAK,CAACnB,QAAAA,EAASH,YAAAA,IAEtBV,GAAW,CACb,CAIA,IAAKG,IAAKA,EAAIP,EAAQQ,OAAQD,IAC5B,GAAmB,MAAfP,EAAQO,GAAY,CACtB,GAAuB,MAAnBP,EAAQO,EAAI,GAAY,CAG1BA,EAAIQ,EAAoBf,IADxBO,GAEA,QACF,CAAO,GAAqB,MAAjBP,EAAQO,EAAE,GAInB,MAFA,IADAA,EAAIE,EAAOT,IAAWO,IAChBG,IAAK,OAAOH,CAItB,MAAO,GAAmB,MAAfP,EAAQO,GAAY,CAC7B,IAAM8B,EAAWC,EAAkBtC,EAASO,GAC5C,IAAiB,GAAb8B,EACF,OAAOzB,EAAe,cAAe,4BAA6BC,EAAyBb,EAASO,IACtGA,EAAI8B,CACN,MACE,IAAoB,IAAhBhC,IAAyBM,EAAaX,EAAQO,IAChD,OAAOK,EAAe,aAAc,wBAAyBC,EAAyBb,EAASO,IAIlF,MAAfP,EAAQO,IACVA,GAQN,CAGF,OAAKH,EAEoB,GAAfD,EAAKK,OACJI,EAAe,aAAc,iBAAiBT,EAAK,GAAGc,QAAQ,KAAMJ,EAAyBb,EAASG,EAAK,GAAGW,gBAC/GX,EAAKK,OAAS,IACbI,EAAe,aAAc,YAChC2B,KAAKC,UAAUrC,EAAKsC,KAAI,SAAAC,GAAC,OAAIA,EAAEzB,OAAO,IAAG,KAAM,GAAG0B,QAAQ,SAAU,IACpE,WAAY,CAACd,KAAM,EAAGK,IAAK,IAN1BtB,EAAe,aAAc,sBAAuB,EAU/D,CAEA,SAASD,EAAaiC,GACpB,MAAgB,MAATA,GAAyB,OAATA,GAA0B,OAATA,GAA2B,OAATA,CAC5D,CAMA,SAASnC,EAAOT,EAASO,GAEvB,IADA,IAAMsC,EAAQtC,EACPA,EAAIP,EAAQQ,OAAQD,IACzB,GAAkB,KAAdP,EAAQO,IAA2B,KAAdP,EAAQO,QAAjC,CAEE,IAAMuC,EAAU9C,EAAQM,OAAOuC,EAAOtC,EAAIsC,GAC1C,GAAItC,EAAI,GAAiB,QAAZuC,EACX,OAAOlC,EAAe,aAAc,6DAA8DC,EAAyBb,EAASO,IAC/H,GAAkB,KAAdP,EAAQO,IAA+B,KAAlBP,EAAQO,EAAI,GAAW,CAErDA,IACA,KACF,CAGF,CAEF,OAAOA,CACT,CAEA,SAASQ,EAAoBf,EAASO,GACpC,GAAIP,EAAQQ,OAASD,EAAI,GAAwB,MAAnBP,EAAQO,EAAI,IAAiC,MAAnBP,EAAQO,EAAI,IAElE,IAAKA,GAAK,EAAGA,EAAIP,EAAQQ,OAAQD,IAC/B,GAAmB,MAAfP,EAAQO,IAAiC,MAAnBP,EAAQO,EAAI,IAAiC,MAAnBP,EAAQO,EAAI,GAAY,CAC1EA,GAAK,EACL,KACF,OAEG,GACLP,EAAQQ,OAASD,EAAI,GACF,MAAnBP,EAAQO,EAAI,IACO,MAAnBP,EAAQO,EAAI,IACO,MAAnBP,EAAQO,EAAI,IACO,MAAnBP,EAAQO,EAAI,IACO,MAAnBP,EAAQO,EAAI,IACO,MAAnBP,EAAQO,EAAI,IACO,MAAnBP,EAAQO,EAAI,GACZ,CACA,IAAIwC,EAAqB,EACzB,IAAKxC,GAAK,EAAGA,EAAIP,EAAQQ,OAAQD,IAC/B,GAAmB,MAAfP,EAAQO,GACVwC,SACK,GAAmB,MAAf/C,EAAQO,IAEU,KAD3BwC,EAEE,KAIR,MAAO,GACL/C,EAAQQ,OAASD,EAAI,GACF,MAAnBP,EAAQO,EAAI,IACO,MAAnBP,EAAQO,EAAI,IACO,MAAnBP,EAAQO,EAAI,IACO,MAAnBP,EAAQO,EAAI,IACO,MAAnBP,EAAQO,EAAI,IACO,MAAnBP,EAAQO,EAAI,IACO,MAAnBP,EAAQO,EAAI,GAEZ,IAAKA,GAAK,EAAGA,EAAIP,EAAQQ,OAAQD,IAC/B,GAAmB,MAAfP,EAAQO,IAAiC,MAAnBP,EAAQO,EAAI,IAAiC,MAAnBP,EAAQO,EAAI,GAAY,CAC1EA,GAAK,EACL,KACF,CAIJ,OAAOA,CACT,CAEA,IAAMyC,EAAc,IACdC,EAAc,IAOpB,SAAS5B,EAAiBrB,EAASO,GAIjC,IAHA,IAAIe,EAAU,GACV4B,EAAY,GACZpB,GAAY,EACTvB,EAAIP,EAAQQ,OAAQD,IAAK,CAC9B,GAAIP,EAAQO,KAAOyC,GAAehD,EAAQO,KAAO0C,EAC7B,KAAdC,EACFA,EAAYlD,EAAQO,GACX2C,IAAclD,EAAQO,KAG/B2C,EAAY,SAET,GAAmB,MAAflD,EAAQO,IACC,KAAd2C,EAAkB,CACpBpB,GAAY,EACZ,KACF,CAEFR,GAAWtB,EAAQO,EACrB,CACA,MAAkB,KAAd2C,GAIG,CACL7D,MAAOiC,EACPC,MAAOhB,EACPuB,UAAWA,EAEf,CAKA,IAAMqB,EAAoB,IAAI3D,OAAO,0DAA2D,KAIhG,SAASkC,EAAwBJ,EAASrB,GAQxC,IAHA,IAAMmD,ED5TD,SAAuB1D,EAAQ2D,GAGpC,IAFA,IAAMD,EAAU,GACZE,EAAQD,EAAM1D,KAAKD,GAChB4D,GAAO,CACZ,IAAMC,EAAa,GACnBA,EAAWC,WAAaH,EAAMI,UAAYH,EAAM,GAAG9C,OAEnD,IADA,IAAMkD,EAAMJ,EAAM9C,OACTe,EAAQ,EAAGA,EAAQmC,EAAKnC,IAC/BgC,EAAWnB,KAAKkB,EAAM/B,IAExB6B,EAAQhB,KAAKmB,GACbD,EAAQD,EAAM1D,KAAKD,EACrB,CACA,OAAO0D,CACT,CC8SkBO,CAAcrC,EAAS6B,GACjCS,EAAY,CAAC,EAEVrD,EAAI,EAAGA,EAAI6C,EAAQ5C,OAAQD,IAAK,CACvC,GAA6B,IAAzB6C,EAAQ7C,GAAG,GAAGC,OAEhB,OAAOI,EAAe,cAAe,cAAcwC,EAAQ7C,GAAG,GAAG,8BAA+BsD,EAAqBT,EAAQ7C,KACxH,QAAsBuD,IAAlBV,EAAQ7C,GAAG,SAAsCuD,IAAlBV,EAAQ7C,GAAG,GACnD,OAAOK,EAAe,cAAe,cAAcwC,EAAQ7C,GAAG,GAAG,sBAAuBsD,EAAqBT,EAAQ7C,KAChH,QAAsBuD,IAAlBV,EAAQ7C,GAAG,KAAqBN,EAAQJ,uBAEjD,OAAOe,EAAe,cAAe,sBAAsBwC,EAAQ7C,GAAG,GAAG,oBAAqBsD,EAAqBT,EAAQ7C,KAK7H,IAAMwD,EAAWX,EAAQ7C,GAAG,GAC5B,IAAKyD,EAAiBD,GACpB,OAAOnD,EAAe,cAAe,cAAcmD,EAAS,wBAAyBF,EAAqBT,EAAQ7C,KAEpH,GAAKqD,EAAU3E,eAAe8E,GAI5B,OAAOnD,EAAe,cAAe,cAAcmD,EAAS,iBAAkBF,EAAqBT,EAAQ7C,KAF3GqD,EAAUG,GAAY,CAI1B,CAEA,OAAO,CACT,CAiBA,SAASzB,EAAkBtC,EAASO,GAGlC,GAAmB,MAAfP,IADJO,GAEE,OAAQ,EACV,GAAmB,MAAfP,EAAQO,GAEV,OAtBJ,SAAiCP,EAASO,GACxC,IAAI0D,EAAK,KAKT,IAJmB,MAAfjE,EAAQO,KACVA,IACA0D,EAAK,cAEA1D,EAAIP,EAAQQ,OAAQD,IAAK,CAC9B,GAAmB,MAAfP,EAAQO,GACV,OAAOA,EACT,IAAKP,EAAQO,GAAG+C,MAAMW,GACpB,KACJ,CACA,OAAQ,CACV,CASWC,CAAwBlE,IAD/BO,GAIF,IADA,IAAI4D,EAAQ,EACL5D,EAAIP,EAAQQ,OAAQD,IAAK4D,IAC9B,KAAInE,EAAQO,GAAG+C,MAAM,OAASa,EAAQ,IAAtC,CAEA,GAAmB,MAAfnE,EAAQO,GACV,MACF,OAAQ,CAHE,CAKZ,OAAOA,CACT,CAEA,SAASK,EAAee,EAAMyC,EAASC,GACrC,MAAO,CACL3D,IAAK,CACHiB,KAAMA,EACNC,IAAKwC,EACLvC,KAAMwC,EAAWxC,MAAQwC,EACzBnC,IAAKmC,EAAWnC,KAGtB,CAEA,SAAS8B,EAAiBD,GACxB,OAAOtE,EAAOsE,EAChB,CASA,SAASlD,EAAyBb,EAASuB,GACzC,IAAM+C,EAAQtE,EAAQmB,UAAU,EAAGI,GAAOgD,MAAM,SAChD,MAAO,CACL1C,KAAMyC,EAAM9D,OAGZ0B,IAAKoC,EAAMA,EAAM9D,OAAS,GAAGA,OAAS,EAE1C,CAGA,SAASqD,EAAqBP,GAC5B,OAAOA,EAAME,WAAaF,EAAM,GAAG9C,MACrC,C", "sources": ["webpack://XMLValidator/webpack/universalModuleDefinition", "webpack://XMLValidator/webpack/bootstrap", "webpack://XMLValidator/webpack/runtime/define property getters", "webpack://XMLValidator/webpack/runtime/hasOwnProperty shorthand", "webpack://XMLValidator/webpack/runtime/make namespace object", "webpack://XMLValidator/./src/util.js", "webpack://XMLValidator/./src/validator.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"XMLValidator\"] = factory();\n\telse\n\t\troot[\"XMLValidator\"] = factory();\n})(this, () => {\nreturn ", "// The require scope\nvar __webpack_require__ = {};\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "'use strict';\n\nconst nameStartChar = ':A-Za-z_\\\\u00C0-\\\\u00D6\\\\u00D8-\\\\u00F6\\\\u00F8-\\\\u02FF\\\\u0370-\\\\u037D\\\\u037F-\\\\u1FFF\\\\u200C-\\\\u200D\\\\u2070-\\\\u218F\\\\u2C00-\\\\u2FEF\\\\u3001-\\\\uD7FF\\\\uF900-\\\\uFDCF\\\\uFDF0-\\\\uFFFD';\nconst nameChar = nameStartChar + '\\\\-.\\\\d\\\\u00B7\\\\u0300-\\\\u036F\\\\u203F-\\\\u2040';\nexport const nameRegexp = '[' + nameStartChar + '][' + nameChar + ']*';\nconst regexName = new RegExp('^' + nameRegexp + '$');\n\nexport function getAllMatches(string, regex) {\n  const matches = [];\n  let match = regex.exec(string);\n  while (match) {\n    const allmatches = [];\n    allmatches.startIndex = regex.lastIndex - match[0].length;\n    const len = match.length;\n    for (let index = 0; index < len; index++) {\n      allmatches.push(match[index]);\n    }\n    matches.push(allmatches);\n    match = regex.exec(string);\n  }\n  return matches;\n}\n\nexport const isName = function(string) {\n  const match = regexName.exec(string);\n  return !(match === null || typeof match === 'undefined');\n}\n\nexport function isExist(v) {\n  return typeof v !== 'undefined';\n}\n\nexport function isEmptyObject(obj) {\n  return Object.keys(obj).length === 0;\n}\n\n/**\n * Copy all the properties of a into b.\n * @param {*} target\n * @param {*} a\n */\nexport function merge(target, a, arrayMode) {\n  if (a) {\n    const keys = Object.keys(a); // will return an array of own properties\n    const len = keys.length; //don't make it inline\n    for (let i = 0; i < len; i++) {\n      if (arrayMode === 'strict') {\n        target[keys[i]] = [ a[keys[i]] ];\n      } else {\n        target[keys[i]] = a[keys[i]];\n      }\n    }\n  }\n}\n/* exports.merge =function (b,a){\n  return Object.assign(b,a);\n} */\n\nexport function getValue(v) {\n  if (exports.isExist(v)) {\n    return v;\n  } else {\n    return '';\n  }\n}\n\n// const fakeCall = function(a) {return a;};\n// const fakeCallNoReturn = function() {};", "'use strict';\n\nimport {getAllMatches, isName} from './util.js';\n\nconst defaultOptions = {\n  allowBooleanAttributes: false, //A tag can have attributes without any value\n  unpairedTags: []\n};\n\n//const tagsPattern = new RegExp(\"<\\\\/?([\\\\w:\\\\-_\\.]+)\\\\s*\\/?>\",\"g\");\nexport function validate(xmlData, options) {\n  options = Object.assign({}, defaultOptions, options);\n\n  //xmlData = xmlData.replace(/(\\r\\n|\\n|\\r)/gm,\"\");//make it single line\n  //xmlData = xmlData.replace(/(^\\s*<\\?xml.*?\\?>)/g,\"\");//Remove XML starting tag\n  //xmlData = xmlData.replace(/(<!DOCTYPE[\\s\\w\\\"\\.\\/\\-\\:]+(\\[.*\\])*\\s*>)/g,\"\");//Remove DOCTYPE\n  const tags = [];\n  let tagFound = false;\n\n  //indicates that the root tag has been closed (aka. depth 0 has been reached)\n  let reachedRoot = false;\n\n  if (xmlData[0] === '\\ufeff') {\n    // check for byte order mark (BOM)\n    xmlData = xmlData.substr(1);\n  }\n  \n  for (let i = 0; i < xmlData.length; i++) {\n\n    if (xmlData[i] === '<' && xmlData[i+1] === '?') {\n      i+=2;\n      i = readPI(xmlData,i);\n      if (i.err) return i;\n    }else if (xmlData[i] === '<') {\n      //starting of tag\n      //read until you reach to '>' avoiding any '>' in attribute value\n      let tagStartPos = i;\n      i++;\n      \n      if (xmlData[i] === '!') {\n        i = readCommentAndCDATA(xmlData, i);\n        continue;\n      } else {\n        let closingTag = false;\n        if (xmlData[i] === '/') {\n          //closing tag\n          closingTag = true;\n          i++;\n        }\n        //read tagname\n        let tagName = '';\n        for (; i < xmlData.length &&\n          xmlData[i] !== '>' &&\n          xmlData[i] !== ' ' &&\n          xmlData[i] !== '\\t' &&\n          xmlData[i] !== '\\n' &&\n          xmlData[i] !== '\\r'; i++\n        ) {\n          tagName += xmlData[i];\n        }\n        tagName = tagName.trim();\n        //console.log(tagName);\n\n        if (tagName[tagName.length - 1] === '/') {\n          //self closing tag without attributes\n          tagName = tagName.substring(0, tagName.length - 1);\n          //continue;\n          i--;\n        }\n        if (!validateTagName(tagName)) {\n          let msg;\n          if (tagName.trim().length === 0) {\n            msg = \"Invalid space after '<'.\";\n          } else {\n            msg = \"Tag '\"+tagName+\"' is an invalid name.\";\n          }\n          return getErrorObject('InvalidTag', msg, getLineNumberForPosition(xmlData, i));\n        }\n\n        const result = readAttributeStr(xmlData, i);\n        if (result === false) {\n          return getErrorObject('InvalidAttr', \"Attributes for '\"+tagName+\"' have open quote.\", getLineNumberForPosition(xmlData, i));\n        }\n        let attrStr = result.value;\n        i = result.index;\n\n        if (attrStr[attrStr.length - 1] === '/') {\n          //self closing tag\n          const attrStrStart = i - attrStr.length;\n          attrStr = attrStr.substring(0, attrStr.length - 1);\n          const isValid = validateAttributeString(attrStr, options);\n          if (isValid === true) {\n            tagFound = true;\n            //continue; //text may presents after self closing tag\n          } else {\n            //the result from the nested function returns the position of the error within the attribute\n            //in order to get the 'true' error line, we need to calculate the position where the attribute begins (i - attrStr.length) and then add the position within the attribute\n            //this gives us the absolute index in the entire xml, which we can use to find the line at last\n            return getErrorObject(isValid.err.code, isValid.err.msg, getLineNumberForPosition(xmlData, attrStrStart + isValid.err.line));\n          }\n        } else if (closingTag) {\n          if (!result.tagClosed) {\n            return getErrorObject('InvalidTag', \"Closing tag '\"+tagName+\"' doesn't have proper closing.\", getLineNumberForPosition(xmlData, i));\n          } else if (attrStr.trim().length > 0) {\n            return getErrorObject('InvalidTag', \"Closing tag '\"+tagName+\"' can't have attributes or invalid starting.\", getLineNumberForPosition(xmlData, tagStartPos));\n          } else if (tags.length === 0) {\n            return getErrorObject('InvalidTag', \"Closing tag '\"+tagName+\"' has not been opened.\", getLineNumberForPosition(xmlData, tagStartPos));\n          } else {\n            const otg = tags.pop();\n            if (tagName !== otg.tagName) {\n              let openPos = getLineNumberForPosition(xmlData, otg.tagStartPos);\n              return getErrorObject('InvalidTag',\n                \"Expected closing tag '\"+otg.tagName+\"' (opened in line \"+openPos.line+\", col \"+openPos.col+\") instead of closing tag '\"+tagName+\"'.\",\n                getLineNumberForPosition(xmlData, tagStartPos));\n            }\n\n            //when there are no more tags, we reached the root level.\n            if (tags.length == 0) {\n              reachedRoot = true;\n            }\n          }\n        } else {\n          const isValid = validateAttributeString(attrStr, options);\n          if (isValid !== true) {\n            //the result from the nested function returns the position of the error within the attribute\n            //in order to get the 'true' error line, we need to calculate the position where the attribute begins (i - attrStr.length) and then add the position within the attribute\n            //this gives us the absolute index in the entire xml, which we can use to find the line at last\n            return getErrorObject(isValid.err.code, isValid.err.msg, getLineNumberForPosition(xmlData, i - attrStr.length + isValid.err.line));\n          }\n\n          //if the root level has been reached before ...\n          if (reachedRoot === true) {\n            return getErrorObject('InvalidXml', 'Multiple possible root nodes found.', getLineNumberForPosition(xmlData, i));\n          } else if(options.unpairedTags.indexOf(tagName) !== -1){\n            //don't push into stack\n          } else {\n            tags.push({tagName, tagStartPos});\n          }\n          tagFound = true;\n        }\n\n        //skip tag text value\n        //It may include comments and CDATA value\n        for (i++; i < xmlData.length; i++) {\n          if (xmlData[i] === '<') {\n            if (xmlData[i + 1] === '!') {\n              //comment or CADATA\n              i++;\n              i = readCommentAndCDATA(xmlData, i);\n              continue;\n            } else if (xmlData[i+1] === '?') {\n              i = readPI(xmlData, ++i);\n              if (i.err) return i;\n            } else{\n              break;\n            }\n          } else if (xmlData[i] === '&') {\n            const afterAmp = validateAmpersand(xmlData, i);\n            if (afterAmp == -1)\n              return getErrorObject('InvalidChar', \"char '&' is not expected.\", getLineNumberForPosition(xmlData, i));\n            i = afterAmp;\n          }else{\n            if (reachedRoot === true && !isWhiteSpace(xmlData[i])) {\n              return getErrorObject('InvalidXml', \"Extra text at the end\", getLineNumberForPosition(xmlData, i));\n            }\n          }\n        } //end of reading tag text value\n        if (xmlData[i] === '<') {\n          i--;\n        }\n      }\n    } else {\n      if ( isWhiteSpace(xmlData[i])) {\n        continue;\n      }\n      return getErrorObject('InvalidChar', \"char '\"+xmlData[i]+\"' is not expected.\", getLineNumberForPosition(xmlData, i));\n    }\n  }\n\n  if (!tagFound) {\n    return getErrorObject('InvalidXml', 'Start tag expected.', 1);\n  }else if (tags.length == 1) {\n      return getErrorObject('InvalidTag', \"Unclosed tag '\"+tags[0].tagName+\"'.\", getLineNumberForPosition(xmlData, tags[0].tagStartPos));\n  }else if (tags.length > 0) {\n      return getErrorObject('InvalidXml', \"Invalid '\"+\n          JSON.stringify(tags.map(t => t.tagName), null, 4).replace(/\\r?\\n/g, '')+\n          \"' found.\", {line: 1, col: 1});\n  }\n\n  return true;\n};\n\nfunction isWhiteSpace(char){\n  return char === ' ' || char === '\\t' || char === '\\n'  || char === '\\r';\n}\n/**\n * Read Processing insstructions and skip\n * @param {*} xmlData\n * @param {*} i\n */\nfunction readPI(xmlData, i) {\n  const start = i;\n  for (; i < xmlData.length; i++) {\n    if (xmlData[i] == '?' || xmlData[i] == ' ') {\n      //tagname\n      const tagname = xmlData.substr(start, i - start);\n      if (i > 5 && tagname === 'xml') {\n        return getErrorObject('InvalidXml', 'XML declaration allowed only at the start of the document.', getLineNumberForPosition(xmlData, i));\n      } else if (xmlData[i] == '?' && xmlData[i + 1] == '>') {\n        //check if valid attribut string\n        i++;\n        break;\n      } else {\n        continue;\n      }\n    }\n  }\n  return i;\n}\n\nfunction readCommentAndCDATA(xmlData, i) {\n  if (xmlData.length > i + 5 && xmlData[i + 1] === '-' && xmlData[i + 2] === '-') {\n    //comment\n    for (i += 3; i < xmlData.length; i++) {\n      if (xmlData[i] === '-' && xmlData[i + 1] === '-' && xmlData[i + 2] === '>') {\n        i += 2;\n        break;\n      }\n    }\n  } else if (\n    xmlData.length > i + 8 &&\n    xmlData[i + 1] === 'D' &&\n    xmlData[i + 2] === 'O' &&\n    xmlData[i + 3] === 'C' &&\n    xmlData[i + 4] === 'T' &&\n    xmlData[i + 5] === 'Y' &&\n    xmlData[i + 6] === 'P' &&\n    xmlData[i + 7] === 'E'\n  ) {\n    let angleBracketsCount = 1;\n    for (i += 8; i < xmlData.length; i++) {\n      if (xmlData[i] === '<') {\n        angleBracketsCount++;\n      } else if (xmlData[i] === '>') {\n        angleBracketsCount--;\n        if (angleBracketsCount === 0) {\n          break;\n        }\n      }\n    }\n  } else if (\n    xmlData.length > i + 9 &&\n    xmlData[i + 1] === '[' &&\n    xmlData[i + 2] === 'C' &&\n    xmlData[i + 3] === 'D' &&\n    xmlData[i + 4] === 'A' &&\n    xmlData[i + 5] === 'T' &&\n    xmlData[i + 6] === 'A' &&\n    xmlData[i + 7] === '['\n  ) {\n    for (i += 8; i < xmlData.length; i++) {\n      if (xmlData[i] === ']' && xmlData[i + 1] === ']' && xmlData[i + 2] === '>') {\n        i += 2;\n        break;\n      }\n    }\n  }\n\n  return i;\n}\n\nconst doubleQuote = '\"';\nconst singleQuote = \"'\";\n\n/**\n * Keep reading xmlData until '<' is found outside the attribute value.\n * @param {string} xmlData\n * @param {number} i\n */\nfunction readAttributeStr(xmlData, i) {\n  let attrStr = '';\n  let startChar = '';\n  let tagClosed = false;\n  for (; i < xmlData.length; i++) {\n    if (xmlData[i] === doubleQuote || xmlData[i] === singleQuote) {\n      if (startChar === '') {\n        startChar = xmlData[i];\n      } else if (startChar !== xmlData[i]) {\n        //if vaue is enclosed with double quote then single quotes are allowed inside the value and vice versa\n      } else {\n        startChar = '';\n      }\n    } else if (xmlData[i] === '>') {\n      if (startChar === '') {\n        tagClosed = true;\n        break;\n      }\n    }\n    attrStr += xmlData[i];\n  }\n  if (startChar !== '') {\n    return false;\n  }\n\n  return {\n    value: attrStr,\n    index: i,\n    tagClosed: tagClosed\n  };\n}\n\n/**\n * Select all the attributes whether valid or invalid.\n */\nconst validAttrStrRegxp = new RegExp('(\\\\s*)([^\\\\s=]+)(\\\\s*=)?(\\\\s*([\\'\"])(([\\\\s\\\\S])*?)\\\\5)?', 'g');\n\n//attr, =\"sd\", a=\"amit's\", a=\"sd\"b=\"saf\", ab  cd=\"\"\n\nfunction validateAttributeString(attrStr, options) {\n  //console.log(\"start:\"+attrStr+\":end\");\n\n  //if(attrStr.trim().length === 0) return true; //empty string\n\n  const matches = getAllMatches(attrStr, validAttrStrRegxp);\n  const attrNames = {};\n\n  for (let i = 0; i < matches.length; i++) {\n    if (matches[i][1].length === 0) {\n      //nospace before attribute name: a=\"sd\"b=\"saf\"\n      return getErrorObject('InvalidAttr', \"Attribute '\"+matches[i][2]+\"' has no space in starting.\", getPositionFromMatch(matches[i]))\n    } else if (matches[i][3] !== undefined && matches[i][4] === undefined) {\n      return getErrorObject('InvalidAttr', \"Attribute '\"+matches[i][2]+\"' is without value.\", getPositionFromMatch(matches[i]));\n    } else if (matches[i][3] === undefined && !options.allowBooleanAttributes) {\n      //independent attribute: ab\n      return getErrorObject('InvalidAttr', \"boolean attribute '\"+matches[i][2]+\"' is not allowed.\", getPositionFromMatch(matches[i]));\n    }\n    /* else if(matches[i][6] === undefined){//attribute without value: ab=\n                    return { err: { code:\"InvalidAttr\",msg:\"attribute \" + matches[i][2] + \" has no value assigned.\"}};\n                } */\n    const attrName = matches[i][2];\n    if (!validateAttrName(attrName)) {\n      return getErrorObject('InvalidAttr', \"Attribute '\"+attrName+\"' is an invalid name.\", getPositionFromMatch(matches[i]));\n    }\n    if (!attrNames.hasOwnProperty(attrName)) {\n      //check for duplicate attribute.\n      attrNames[attrName] = 1;\n    } else {\n      return getErrorObject('InvalidAttr', \"Attribute '\"+attrName+\"' is repeated.\", getPositionFromMatch(matches[i]));\n    }\n  }\n\n  return true;\n}\n\nfunction validateNumberAmpersand(xmlData, i) {\n  let re = /\\d/;\n  if (xmlData[i] === 'x') {\n    i++;\n    re = /[\\da-fA-F]/;\n  }\n  for (; i < xmlData.length; i++) {\n    if (xmlData[i] === ';')\n      return i;\n    if (!xmlData[i].match(re))\n      break;\n  }\n  return -1;\n}\n\nfunction validateAmpersand(xmlData, i) {\n  // https://www.w3.org/TR/xml/#dt-charref\n  i++;\n  if (xmlData[i] === ';')\n    return -1;\n  if (xmlData[i] === '#') {\n    i++;\n    return validateNumberAmpersand(xmlData, i);\n  }\n  let count = 0;\n  for (; i < xmlData.length; i++, count++) {\n    if (xmlData[i].match(/\\w/) && count < 20)\n      continue;\n    if (xmlData[i] === ';')\n      break;\n    return -1;\n  }\n  return i;\n}\n\nfunction getErrorObject(code, message, lineNumber) {\n  return {\n    err: {\n      code: code,\n      msg: message,\n      line: lineNumber.line || lineNumber,\n      col: lineNumber.col,\n    },\n  };\n}\n\nfunction validateAttrName(attrName) {\n  return isName(attrName);\n}\n\n// const startsWithXML = /^xml/i;\n\nfunction validateTagName(tagname) {\n  return isName(tagname) /* && !tagname.match(startsWithXML) */;\n}\n\n//this function returns the line number for the character at the given index\nfunction getLineNumberForPosition(xmlData, index) {\n  const lines = xmlData.substring(0, index).split(/\\r?\\n/);\n  return {\n    line: lines.length,\n\n    // column number is last line's length + 1, because column numbering starts at 1:\n    col: lines[lines.length - 1].length + 1\n  };\n}\n\n//this function returns the position of the first character of match within attrStr\nfunction getPositionFromMatch(match) {\n  return match.startIndex + match[1].length;\n}\n"], "names": ["root", "factory", "exports", "module", "define", "amd", "this", "__webpack_require__", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "Symbol", "toStringTag", "value", "nameStartChar", "regexName", "RegExp", "isName", "string", "exec", "defaultOptions", "allowBooleanAttributes", "unpairedTags", "validate", "xmlData", "options", "assign", "tags", "tagFound", "reachedRoot", "substr", "i", "length", "readPI", "err", "isWhiteSpace", "getErrorObject", "getLineNumberForPosition", "tagStartPos", "readCommentAndCDATA", "closingTag", "tagName", "trim", "substring", "result", "readAttributeStr", "attrStr", "index", "attrStrStart", "<PERSON><PERSON><PERSON><PERSON>", "validateAttributeString", "code", "msg", "line", "tagClosed", "otg", "pop", "openPos", "col", "indexOf", "push", "afterAmp", "validateAmpersand", "JSON", "stringify", "map", "t", "replace", "char", "start", "tagname", "angleBracketsCount", "doubleQuote", "singleQuote", "startChar", "validAttrStrRegxp", "matches", "regex", "match", "allmatches", "startIndex", "lastIndex", "len", "getAllMatches", "attrNames", "getPositionFromMatch", "undefined", "attrName", "validateAttrName", "re", "validateNumberAmpersand", "count", "message", "lineNumber", "lines", "split"], "sourceRoot": ""}