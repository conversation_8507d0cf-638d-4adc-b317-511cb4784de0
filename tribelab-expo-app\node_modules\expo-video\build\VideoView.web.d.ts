import React from 'react';
import VideoPlayer from './VideoPlayer.web';
import type { VideoViewProps } from './VideoView.types';
export declare function isPictureInPictureSupported(): boolean;
export declare const VideoView: React.ForwardRefExoticComponent<{
    player?: VideoPlayer;
} & VideoViewProps & React.RefAttributes<unknown>>;
export default VideoView;
//# sourceMappingURL=VideoView.web.d.ts.map