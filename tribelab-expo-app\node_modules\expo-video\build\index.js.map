{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,2BAA2B,EAC3B,oBAAoB,EACpB,sBAAsB,EACtB,wBAAwB,GACzB,MAAM,eAAe,CAAC;AACvB,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AACxC,OAAO,EAAE,cAAc,EAAE,MAAM,eAAe,CAAC;AAG/C,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAElD,OAAO,EAAE,iBAAiB,EAAE,MAAM,eAAe,CAAC;AAElD,OAAO,EACL,WAAW,GAeZ,MAAM,qBAAqB,CAAC", "sourcesContent": ["export {\n  isPictureInPictureSupported,\n  clearVideoCacheAsync,\n  setVideoCacheSizeAsync,\n  getCurrentVideoCacheSize,\n} from './VideoModule';\nexport { VideoView } from './VideoView';\nexport { useVideoPlayer } from './VideoPlayer';\n\nexport { VideoContentFit, VideoViewProps, SurfaceType } from './VideoView.types';\nexport { VideoThumbnail } from './VideoThumbnail';\n\nexport { createVideoPlayer } from './VideoPlayer';\n\nexport {\n  VideoPlayer,\n  VideoPlayerStatus,\n  VideoSource,\n  PlayerError,\n  VideoMetadata,\n  DRMType,\n  DRMOptions,\n  BufferOptions,\n  AudioMixingMode,\n  VideoThumbnailOptions,\n  VideoSize,\n  SubtitleTrack,\n  AudioTrack,\n  VideoTrack,\n  ContentType,\n} from './VideoPlayer.types';\n\nexport {\n  VideoPlayerEvents,\n  StatusChangeEventPayload,\n  PlayingChangeEventPayload,\n  PlaybackRateChangeEventPayload,\n  VolumeChangeEventPayload,\n  MutedChangeEventPayload,\n  TimeUpdateEventPayload,\n  SourceChangeEventPayload,\n  SourceLoadEventPayload,\n} from './VideoPlayerEvents.types';\n"]}