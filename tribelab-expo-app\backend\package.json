{"name": "tribelab-mobile-backend", "version": "1.0.0", "description": "Standalone Express backend for TribeLab mobile app", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"@aws-sdk/client-s3": "^3.844.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.6.0", "express": "^4.21.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "sharp": "^0.34.3", "slugify": "^1.6.6", "socket.io": "^4.8.1", "uuid": "^11.1.0"}, "devDependencies": {"@types/node": "^20.17.16", "jest": "^29.7.0", "nodemon": "^3.0.2"}, "keywords": ["express", "mongodb", "mobile", "api", "tribelab"], "author": "TribeLab Team", "license": "MIT"}