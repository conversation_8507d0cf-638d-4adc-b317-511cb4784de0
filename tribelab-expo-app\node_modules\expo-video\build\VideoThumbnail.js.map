{"version": 3, "file": "VideoThumbnail.js", "sourceRoot": "", "sources": ["../src/VideoThumbnail.ts"], "names": [], "mappings": "AAEA,OAAO,iBAAiB,MAAM,qBAAqB,CAAC;AA4BpD,eAAe,iBAAiB,CAAC,cAAc,CAAC", "sourcesContent": ["import { SharedRef } from 'expo';\n\nimport NativeVideoModule from './NativeVideoModule';\n\n/**\n * Represents a video thumbnail that references a native image.\n * Instances of this class can be passed as a source to the `Image` component from `expo-image`.\n * @platform android\n * @platform ios\n */\nexport declare class VideoThumbnail extends SharedRef<'image'> {\n  /**\n   * Width of the created thumbnail.\n   */\n  width: number;\n  /**\n   * Height of the created thumbnail.\n   */\n  height: number;\n  /**\n   * The time in seconds at which the thumbnail was to be created.\n   */\n  requestedTime: number;\n  /**\n   * The time in seconds at which the thumbnail was actually generated.\n   * @platform ios\n   */\n  actualTime: number;\n}\n\nexport default NativeVideoModule.VideoThumbnail;\n"]}