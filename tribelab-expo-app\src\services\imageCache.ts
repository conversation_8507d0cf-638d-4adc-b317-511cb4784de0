import AsyncStorage from '@react-native-async-storage/async-storage';
import * as FileSystem from 'expo-file-system';
import * as Crypto from 'expo-crypto';

interface CacheEntry {
  url: string;
  localPath: string;
  timestamp: number;
  size: number;
  expiresAt: number;
}

interface CacheConfig {
  maxCacheSize: number; // in bytes
  maxCacheAge: number; // in milliseconds
  maxEntries: number;
}

class ImageCacheService {
  private cacheDir: string;
  private cacheIndex: Map<string, CacheEntry> = new Map();
  private config: CacheConfig;
  private initialized = false;

  constructor() {
    this.cacheDir = `${FileSystem.cacheDirectory}images/`;
    this.config = {
      maxCacheSize: 100 * 1024 * 1024, // 100MB
      maxCacheAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      maxEntries: 1000,
    };
  }

  /**
   * Initialize the cache service
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      // Ensure cache directory exists
      const dirInfo = await FileSystem.getInfoAsync(this.cacheDir);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(this.cacheDir, { intermediates: true });
      }

      // Load cache index from storage
      await this.loadCacheIndex();
      
      // Clean up expired entries
      await this.cleanupExpiredEntries();
      
      this.initialized = true;
      console.log('✅ Image cache service initialized');
    } catch (error) {
      console.error('❌ Failed to initialize image cache:', error);
    }
  }

  /**
   * Get cached image path or download and cache if not available
   */
  async getCachedImage(url: string): Promise<string> {
    if (!this.initialized) {
      await this.initialize();
    }

    const cacheKey = await this.generateCacheKey(url);
    const cachedEntry = this.cacheIndex.get(cacheKey);

    // Check if we have a valid cached entry
    if (cachedEntry && await this.isValidCacheEntry(cachedEntry)) {
      // Update access time
      cachedEntry.timestamp = Date.now();
      await this.saveCacheIndex();
      return cachedEntry.localPath;
    }

    // Download and cache the image
    return await this.downloadAndCache(url, cacheKey);
  }

  /**
   * Preload images for better performance
   */
  async preloadImages(urls: string[]): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }

    const downloadPromises = urls.map(url => 
      this.getCachedImage(url).catch(error => {
        console.warn(`Failed to preload image: ${url}`, error);
        return null;
      })
    );

    await Promise.all(downloadPromises);
    console.log(`✅ Preloaded ${urls.length} images`);
  }

  /**
   * Clear all cached images
   */
  async clearCache(): Promise<void> {
    try {
      // Remove all files from cache directory
      const files = await FileSystem.readDirectoryAsync(this.cacheDir);
      const deletePromises = files.map(file => 
        FileSystem.deleteAsync(`${this.cacheDir}${file}`, { idempotent: true })
      );
      
      await Promise.all(deletePromises);
      
      // Clear cache index
      this.cacheIndex.clear();
      await this.saveCacheIndex();
      
      console.log('✅ Image cache cleared');
    } catch (error) {
      console.error('❌ Failed to clear image cache:', error);
    }
  }

  /**
   * Get cache statistics
   */
  async getCacheStats(): Promise<{
    totalSize: number;
    totalEntries: number;
    oldestEntry: number;
    newestEntry: number;
  }> {
    let totalSize = 0;
    let oldestEntry = Date.now();
    let newestEntry = 0;

    for (const entry of this.cacheIndex.values()) {
      totalSize += entry.size;
      oldestEntry = Math.min(oldestEntry, entry.timestamp);
      newestEntry = Math.max(newestEntry, entry.timestamp);
    }

    return {
      totalSize,
      totalEntries: this.cacheIndex.size,
      oldestEntry,
      newestEntry,
    };
  }

  /**
   * Remove specific image from cache
   */
  async removeFromCache(url: string): Promise<void> {
    const cacheKey = await this.generateCacheKey(url);
    const entry = this.cacheIndex.get(cacheKey);
    
    if (entry) {
      try {
        await FileSystem.deleteAsync(entry.localPath, { idempotent: true });
        this.cacheIndex.delete(cacheKey);
        await this.saveCacheIndex();
      } catch (error) {
        console.error('Failed to remove image from cache:', error);
      }
    }
  }

  /**
   * Generate cache key from URL
   */
  private async generateCacheKey(url: string): Promise<string> {
    return await Crypto.digestStringAsync(Crypto.CryptoDigestAlgorithm.MD5, url);
  }

  /**
   * Download and cache image
   */
  private async downloadAndCache(url: string, cacheKey: string): Promise<string> {
    try {
      const localPath = `${this.cacheDir}${cacheKey}.jpg`;
      
      // Download the image
      const downloadResult = await FileSystem.downloadAsync(url, localPath);
      
      if (downloadResult.status !== 200) {
        throw new Error(`Download failed with status ${downloadResult.status}`);
      }

      // Get file info
      const fileInfo = await FileSystem.getInfoAsync(localPath);
      
      if (!fileInfo.exists) {
        throw new Error('Downloaded file does not exist');
      }

      // Create cache entry
      const cacheEntry: CacheEntry = {
        url,
        localPath,
        timestamp: Date.now(),
        size: fileInfo.size || 0,
        expiresAt: Date.now() + this.config.maxCacheAge,
      };

      // Add to cache index
      this.cacheIndex.set(cacheKey, cacheEntry);
      
      // Save cache index
      await this.saveCacheIndex();
      
      // Check if we need to cleanup cache
      await this.cleanupIfNeeded();
      
      return localPath;
    } catch (error) {
      console.error('Failed to download and cache image:', error);
      // Return original URL as fallback
      return url;
    }
  }

  /**
   * Check if cache entry is valid
   */
  private async isValidCacheEntry(entry: CacheEntry): Promise<boolean> {
    // Check if expired
    if (Date.now() > entry.expiresAt) {
      return false;
    }

    // Check if file still exists
    const fileInfo = await FileSystem.getInfoAsync(entry.localPath);
    return fileInfo.exists;
  }

  /**
   * Load cache index from storage
   */
  private async loadCacheIndex(): Promise<void> {
    try {
      const indexData = await AsyncStorage.getItem('image_cache_index');
      if (indexData) {
        const entries = JSON.parse(indexData);
        this.cacheIndex = new Map(entries);
      }
    } catch (error) {
      console.error('Failed to load cache index:', error);
      this.cacheIndex = new Map();
    }
  }

  /**
   * Save cache index to storage
   */
  private async saveCacheIndex(): Promise<void> {
    try {
      const entries = Array.from(this.cacheIndex.entries());
      await AsyncStorage.setItem('image_cache_index', JSON.stringify(entries));
    } catch (error) {
      console.error('Failed to save cache index:', error);
    }
  }

  /**
   * Clean up expired entries
   */
  private async cleanupExpiredEntries(): Promise<void> {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.cacheIndex.entries()) {
      if (now > entry.expiresAt) {
        expiredKeys.push(key);
        // Delete the file
        try {
          await FileSystem.deleteAsync(entry.localPath, { idempotent: true });
        } catch (error) {
          console.warn('Failed to delete expired cache file:', error);
        }
      }
    }

    // Remove expired entries from index
    expiredKeys.forEach(key => this.cacheIndex.delete(key));
    
    if (expiredKeys.length > 0) {
      await this.saveCacheIndex();
      console.log(`🧹 Cleaned up ${expiredKeys.length} expired cache entries`);
    }
  }

  /**
   * Cleanup cache if it exceeds limits
   */
  private async cleanupIfNeeded(): Promise<void> {
    const stats = await this.getCacheStats();
    
    // Check if we need to cleanup by size or entry count
    if (stats.totalSize > this.config.maxCacheSize || 
        stats.totalEntries > this.config.maxEntries) {
      
      await this.performCleanup();
    }
  }

  /**
   * Perform cache cleanup (LRU strategy)
   */
  private async performCleanup(): Promise<void> {
    // Sort entries by timestamp (oldest first)
    const sortedEntries = Array.from(this.cacheIndex.entries())
      .sort(([, a], [, b]) => a.timestamp - b.timestamp);

    const targetSize = this.config.maxCacheSize * 0.8; // Clean to 80% of max size
    const targetEntries = this.config.maxEntries * 0.8; // Clean to 80% of max entries
    
    let currentSize = 0;
    let currentEntries = 0;
    const entriesToKeep = new Map<string, CacheEntry>();

    // Keep newest entries within limits
    for (let i = sortedEntries.length - 1; i >= 0; i--) {
      const [key, entry] = sortedEntries[i];
      
      if (currentSize + entry.size <= targetSize && currentEntries < targetEntries) {
        entriesToKeep.set(key, entry);
        currentSize += entry.size;
        currentEntries++;
      } else {
        // Delete old entry
        try {
          await FileSystem.deleteAsync(entry.localPath, { idempotent: true });
        } catch (error) {
          console.warn('Failed to delete cache file during cleanup:', error);
        }
      }
    }

    // Update cache index
    this.cacheIndex = entriesToKeep;
    await this.saveCacheIndex();
    
    const removedCount = sortedEntries.length - entriesToKeep.size;
    console.log(`🧹 Cache cleanup: removed ${removedCount} entries`);
  }
}

// Export singleton instance
export const imageCache = new ImageCacheService();

// Export utility functions
export const preloadImages = (urls: string[]) => imageCache.preloadImages(urls);
export const clearImageCache = () => imageCache.clearCache();
export const getCachedImagePath = (url: string) => imageCache.getCachedImage(url);
export const getImageCacheStats = () => imageCache.getCacheStats();
