import { S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3';
import { v4 as uuidv4 } from 'uuid';
import sharp from 'sharp';
import crypto from 'crypto';

// Initialize Cloudflare R2 client (using S3-compatible API)
const r2Client = new S3Client({
  region: 'auto', // Cloudflare R2 uses 'auto' region
  endpoint: process.env.R2_ENDPOINT_URL,
  credentials: {
    accessKeyId: process.env.R2_ACCESS_KEY_ID,
    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY,
  },
  forcePathStyle: true, // Required for R2 compatibility
});

// Image optimization settings
const IMAGE_QUALITY = 85;
const MAX_WIDTH = 1920;
const MAX_HEIGHT = 1080;
const THUMBNAIL_SIZE = 300;

/**
 * Generate a unique filename with proper extension
 */
const generateFileName = (originalName, prefix = '') => {
  const extension = originalName.split('.').pop().toLowerCase();
  const uniqueId = uuidv4();
  const timestamp = Date.now();
  
  return prefix 
    ? `${prefix}/${timestamp}-${uniqueId}.${extension}`
    : `${timestamp}-${uniqueId}.${extension}`;
};

/**
 * Optimize image for web/mobile display
 */
const optimizeImage = async (buffer, options = {}) => {
  const {
    width = MAX_WIDTH,
    height = MAX_HEIGHT,
    quality = IMAGE_QUALITY,
    format = 'jpeg'
  } = options;

  try {
    const optimized = await sharp(buffer)
      .resize(width, height, {
        fit: 'inside',
        withoutEnlargement: true
      })
      .jpeg({ quality, progressive: true })
      .toBuffer();

    return optimized;
  } catch (error) {
    console.error('Image optimization failed:', error);
    throw new Error('Failed to optimize image');
  }
};

/**
 * Create thumbnail from image buffer
 */
const createThumbnail = async (buffer) => {
  try {
    const thumbnail = await sharp(buffer)
      .resize(THUMBNAIL_SIZE, THUMBNAIL_SIZE, {
        fit: 'cover',
        position: 'center'
      })
      .jpeg({ quality: 80 })
      .toBuffer();

    return thumbnail;
  } catch (error) {
    console.error('Thumbnail creation failed:', error);
    throw new Error('Failed to create thumbnail');
  }
};

/**
 * Upload file to Cloudflare R2
 */
export const uploadToR2 = async (buffer, originalName, options = {}) => {
  try {
    const {
      folder = 'uploads',
      optimize = true,
      createThumbnails = false,
      contentType = 'image/jpeg'
    } = options;

    // Generate unique filename
    const fileName = generateFileName(originalName, folder);
    
    // Optimize image if requested
    let finalBuffer = buffer;
    if (optimize && contentType.startsWith('image/')) {
      finalBuffer = await optimizeImage(buffer);
    }

    // Upload main image
    const uploadCommand = new PutObjectCommand({
      Bucket: process.env.R2_BUCKET_NAME,
      Key: fileName,
      Body: finalBuffer,
      ContentType: contentType,
      CacheControl: 'public, max-age=31536000', // 1 year cache
      Metadata: {
        'original-name': originalName,
        'upload-timestamp': Date.now().toString(),
      }
    });

    await r2Client.send(uploadCommand);

    const result = {
      fileName,
      url: `${process.env.R2_PUBLIC_URL}/${fileName}`,
      size: finalBuffer.length,
      contentType
    };

    // Create thumbnail if requested
    if (createThumbnails && contentType.startsWith('image/')) {
      try {
        const thumbnailBuffer = await createThumbnail(buffer);
        const thumbnailName = generateFileName(originalName, `${folder}/thumbnails`);
        
        const thumbnailCommand = new PutObjectCommand({
          Bucket: process.env.R2_BUCKET_NAME,
          Key: thumbnailName,
          Body: thumbnailBuffer,
          ContentType: 'image/jpeg',
          CacheControl: 'public, max-age=31536000',
          Metadata: {
            'original-name': originalName,
            'is-thumbnail': 'true',
            'parent-image': fileName,
          }
        });

        await r2Client.send(thumbnailCommand);
        
        result.thumbnail = {
          fileName: thumbnailName,
          url: `${process.env.R2_PUBLIC_URL}/${thumbnailName}`,
          size: thumbnailBuffer.length
        };
      } catch (thumbnailError) {
        console.warn('Thumbnail creation failed, continuing without thumbnail:', thumbnailError);
      }
    }

    console.log(`✅ File uploaded to R2: ${fileName}`);
    return result;

  } catch (error) {
    console.error('R2 upload failed:', error);
    throw new Error(`Failed to upload to R2: ${error.message}`);
  }
};

/**
 * Delete file from Cloudflare R2
 */
export const deleteFromR2 = async (fileName) => {
  try {
    const deleteCommand = new DeleteObjectCommand({
      Bucket: process.env.R2_BUCKET_NAME,
      Key: fileName,
    });

    await r2Client.send(deleteCommand);
    console.log(`✅ File deleted from R2: ${fileName}`);
    return true;

  } catch (error) {
    console.error('R2 deletion failed:', error);
    throw new Error(`Failed to delete from R2: ${error.message}`);
  }
};

/**
 * Upload user profile image
 */
export const uploadProfileImage = async (buffer, originalName, userId) => {
  try {
    const result = await uploadToR2(buffer, originalName, {
      folder: `profiles/${userId}`,
      optimize: true,
      createThumbnails: true,
      contentType: 'image/jpeg'
    });

    return result;
  } catch (error) {
    console.error('Profile image upload failed:', error);
    throw new Error('Failed to upload profile image');
  }
};

/**
 * Upload community image (banner/icon)
 */
export const uploadCommunityImage = async (buffer, originalName, communityId, type = 'banner') => {
  try {
    const result = await uploadToR2(buffer, originalName, {
      folder: `communities/${communityId}/${type}`,
      optimize: true,
      createThumbnails: type === 'banner',
      contentType: 'image/jpeg'
    });

    return result;
  } catch (error) {
    console.error('Community image upload failed:', error);
    throw new Error('Failed to upload community image');
  }
};

/**
 * Upload course thumbnail
 */
export const uploadCourseThumbnail = async (buffer, originalName, courseId) => {
  try {
    const result = await uploadToR2(buffer, originalName, {
      folder: `courses/${courseId}/thumbnails`,
      optimize: true,
      createThumbnails: true,
      contentType: 'image/jpeg'
    });

    return result;
  } catch (error) {
    console.error('Course thumbnail upload failed:', error);
    throw new Error('Failed to upload course thumbnail');
  }
};

/**
 * Get file info from R2 (for validation)
 */
export const getR2FileInfo = async (fileName) => {
  try {
    const command = new GetObjectCommand({
      Bucket: process.env.R2_BUCKET_NAME,
      Key: fileName,
    });

    const response = await r2Client.send(command);
    
    return {
      exists: true,
      size: response.ContentLength,
      contentType: response.ContentType,
      lastModified: response.LastModified,
      metadata: response.Metadata
    };

  } catch (error) {
    if (error.name === 'NoSuchKey') {
      return { exists: false };
    }
    throw error;
  }
};

/**
 * Validate image file
 */
export const validateImageFile = (buffer, maxSize = 10 * 1024 * 1024) => { // 10MB default
  if (!buffer || buffer.length === 0) {
    throw new Error('No file data provided');
  }

  if (buffer.length > maxSize) {
    throw new Error(`File too large. Maximum size is ${maxSize / (1024 * 1024)}MB`);
  }

  // Check if it's a valid image by trying to process it with sharp
  return sharp(buffer).metadata()
    .then(metadata => {
      if (!metadata.format) {
        throw new Error('Invalid image format');
      }
      
      const allowedFormats = ['jpeg', 'jpg', 'png', 'webp'];
      if (!allowedFormats.includes(metadata.format.toLowerCase())) {
        throw new Error(`Unsupported image format: ${metadata.format}`);
      }

      return {
        valid: true,
        format: metadata.format,
        width: metadata.width,
        height: metadata.height,
        size: buffer.length
      };
    })
    .catch(error => {
      throw new Error(`Invalid image file: ${error.message}`);
    });
};
