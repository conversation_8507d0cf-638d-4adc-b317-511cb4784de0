{"version": 3, "file": "VideoPlayer.types.js", "sourceRoot": "", "sources": ["../src/VideoPlayer.types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { SharedObject } from 'expo';\n\nimport { VideoPlayerEvents } from './VideoPlayerEvents.types';\nimport { VideoThumbnail } from './VideoThumbnail';\n\n/**\n * A class that represents an instance of the video player.\n */\nexport declare class VideoPlayer extends SharedObject<VideoPlayerEvents> {\n  /**\n   * Boolean value whether the player is currently playing.\n   * > Use `play` and `pause` methods to control the playback.\n   */\n  readonly playing: boolean;\n\n  /**\n   * Determines whether the player should automatically replay after reaching the end of the video.\n   * @default false\n   */\n  loop: boolean;\n\n  /**\n   * Determines whether the player should allow external playback.\n   * @default true\n   * @platform ios\n   */\n  allowsExternalPlayback: boolean;\n\n  /**\n   * Determines how the player will interact with other audio playing in the system.\n   *\n   * @default 'auto'\n   * @platform android\n   * @platform ios\n   */\n  audioMixingMode: AudioMixingMode;\n\n  /**\n   * Boolean value whether the player is currently muted.\n   * Setting this property to `true`/`false` will mute/unmute the player.\n   * @default false\n   */\n  muted: boolean;\n\n  /**\n   * Float value indicating the current playback time in seconds.\n   *\n   * If the player is not yet playing, this value indicates the time position\n   * at which playback will begin once the `play()` method is called.\n   *\n   * Setting `currentTime` to a new value seeks the player to the given time.\n   * Note that frame accurate seeking may incur additional decoding delay which can impact seeking performance.\n   * Consider using the [`seekBy`](#seekbyseconds) function if the time does not have to be set precisely.\n   */\n  currentTime: number;\n\n  /**\n   * The exact timestamp when the currently displayed video frame was sent from the server,\n   * based on the `EXT-X-PROGRAM-DATE-TIME` tag in the livestream metadata.\n   * If this metadata is missing, this property will return `null`.\n   * @platform android\n   * @platform ios\n   */\n  readonly currentLiveTimestamp: number | null;\n\n  /**\n   * Float value indicating the latency of the live stream in seconds.\n   * If a livestream doesn't have the required metadata, this will return `null`.\n   * @platform android\n   * @platform ios\n   */\n  readonly currentOffsetFromLive: number | null;\n\n  /**\n   * Float value indicating the time offset from the live in seconds.\n   * @platform ios\n   */\n  targetOffsetFromLive: number;\n\n  /**\n   * Float value indicating the duration of the current video in seconds.\n   */\n  readonly duration: number;\n\n  /**\n   * Float value between `0` and `1.0` representing the current volume.\n   * Muting the player doesn't affect the volume. In other words, when the player is muted, the volume is the same as\n   * when unmuted. Similarly, setting the volume doesn't unmute the player.\n   * @default 1.0\n   */\n  volume: number;\n\n  /**\n   * Boolean value indicating if the player should correct audio pitch when the playback speed changes.\n   * @default true\n   */\n  preservesPitch: boolean;\n\n  /**\n   * Float value indicating the interval in seconds at which the player will emit the [`timeUpdate`](#videoplayerevents) event.\n   * When the value is equal to `0`, the event will not be emitted.\n   *\n   * @default 0\n   */\n  timeUpdateEventInterval: number;\n\n  /**\n   * Float value between `0` and `16.0` indicating the current playback speed of the player.\n   * @default 1.0\n   */\n  playbackRate: number;\n\n  /**\n   * Boolean value indicating whether the player is currently playing a live stream.\n   */\n  readonly isLive: boolean;\n\n  /**\n   * Indicates the current status of the player.\n   */\n  readonly status: VideoPlayerStatus;\n\n  /**\n   * Boolean value determining whether the player should show the now playing notification.\n   *\n   * @default false\n   * @platform android\n   * @platform ios\n   */\n  showNowPlayingNotification: boolean;\n\n  /**\n   * Determines whether the player should continue playing after the app enters the background.\n   * @default false\n   * @platform ios\n   * @platform android\n   */\n  staysActiveInBackground: boolean;\n\n  /**\n   * Float value indicating how far the player has buffered the video in seconds.\n   *\n   * This value is 0 when the player has not buffered up to the current playback time.\n   * When it's impossible to determine the buffer state (for example, when the player isn't playing any media), this value is -1.\n   */\n  readonly bufferedPosition: number;\n\n  /**\n   * Specifies buffer options which will be used by the player when buffering the video.\n   *\n   * > You should provide a `BufferOptions` object when setting this property. Setting individual buffer properties is not supported.\n   * @platform android\n   * @platform ios\n   */\n  bufferOptions: BufferOptions;\n\n  /**\n   * Specifies the subtitle track which is currently displayed by the player. `null` when no subtitles are displayed.\n   *\n   * > To ensure a valid subtitle track, always assign one of the subtitle tracks from the [`availableSubtitleTracks`](#availablesubtitletracks) array.\n   *\n   * @default null\n   * @platform android\n   * @platform ios\n   */\n  subtitleTrack: SubtitleTrack | null;\n\n  /**\n   * Specifies the audio track currently played by the player. `null` when no audio is played.\n   *\n   * @default null\n   * @platform android\n   * @platform ios\n   */\n  audioTrack: AudioTrack | null;\n\n  /**\n   * An array of audio tracks available for the current video.\n   *\n   * @platform android\n   * @platform ios\n   */\n  readonly availableAudioTracks: AudioTrack[];\n\n  /**\n   * An array of subtitle tracks available for the current video.\n   *\n   * @platform android\n   * @platform ios\n   */\n  readonly availableSubtitleTracks: SubtitleTrack[];\n\n  /**\n   * Specifies the video track currently played by the player. `null` when no video is displayed.\n   *\n   * @default null\n   * @platform android\n   * @platform ios\n   */\n  readonly videoTrack: VideoTrack | null;\n\n  /**\n   * An array of video tracks available for the current video.\n   *\n   * > On iOS, when using a HLS source, make sure that the uri contains `.m3u8` extension or that the [`contentType`](#contenttype) property of the [`VideoSource`](#videosource) has been set to `'hls'`. Otherwise, the video tracks will not be available.\n   *\n   * @platform android\n   * @platform ios\n   */\n  readonly availableVideoTracks: VideoTrack[];\n\n  /**\n   * Initializes a new video player instance with the given source.\n   *\n   * @param source The source of the video to be played.\n   * @param useSynchronousReplace Optional parameter, when `true` `source` from the first parameter will be loaded on the main thread.\n   * @hidden\n   */\n  constructor(source: VideoSource, useSynchronousReplace?: boolean);\n\n  /**\n   * Resumes the player.\n   */\n  play(): void;\n\n  /**\n   * Pauses the player.\n   */\n  pause(): void;\n\n  /**\n   * Replaces the current source with a new one.\n   *\n   * > On iOS, this method loads the asset data synchronously on the UI thread and can block it for extended periods of time.\n   * > Use `replaceAsync` to load the asset asynchronously and avoid UI lags.\n   *\n   * > This method will be deprecated in the future.\n   */\n  replace(source: VideoSource, disableWarning?: boolean): void;\n\n  /**\n   * Replaces the current source with a new one, while offloading loading of the asset to a different thread.\n   *\n   * > On Android and Web, this method is equivalent to `replace`.\n   */\n  replaceAsync(source: VideoSource): Promise<void>;\n\n  /**\n   * Seeks the playback by the given number of seconds. The time to which the player seeks may differ from the specified requested time for efficiency,\n   * depending on the encoding and what is currently buffered by the player. Use this function to implement playback controls that seek by specific amount of time,\n   * in which case, the actual time usually does not have to be precise. For frame accurate seeking, use the [`currentTime`](#currenttime) property.\n   */\n  seekBy(seconds: number): void;\n\n  /**\n   * Seeks the playback to the beginning.\n   */\n  replay(): void;\n\n  /**\n   * Generates thumbnails from the currently played asset. The thumbnails are references to native images,\n   * thus they can be used as a source of the `Image` component from `expo-image`.\n   * @platform android\n   * @platform ios\n   */\n  generateThumbnailsAsync(\n    times: number | number[],\n    options?: VideoThumbnailOptions\n  ): Promise<VideoThumbnail[]>;\n}\n\n/**\n * Additional options for video thumbnails generation.\n */\nexport type VideoThumbnailOptions = {\n  /**\n   * If provided, the generated thumbnail will not exceed this width in pixels, preserving its aspect ratio.\n   * @platform android\n   * @platform ios\n   */\n  maxWidth?: number;\n\n  /**\n   * If provided, the generated thumbnail will not exceed this height in pixels, preserving its aspect ratio.\n   * @platform android\n   * @platform ios\n   */\n  maxHeight?: number;\n};\n\n/**\n * Describes the current status of the player.\n * - `idle`: The player is not playing or loading any videos.\n * - `loading`: The player is loading video data from the provided source\n * - `readyToPlay`: The player has loaded enough data to start playing or to continue playback.\n * - `error`: The player has encountered an error while loading or playing the video.\n */\nexport type VideoPlayerStatus = 'idle' | 'loading' | 'readyToPlay' | 'error';\n\nexport type VideoSource =\n  | string\n  | number\n  | null\n  | {\n      /**\n       * The URI of the video.\n       *\n       * This property is exclusive with the `assetId` property. When both are present, the `assetId` will be ignored.\n       */\n      uri?: string;\n\n      /**\n       * The asset ID of a local video asset, acquired with the `require` function.\n       * This property is exclusive with the `uri` property. When both are present, the `assetId` will be ignored.\n       */\n      assetId?: number;\n\n      /**\n       * Specifies the DRM options which will be used by the player while loading the video.\n       */\n      drm?: DRMOptions;\n\n      /**\n       * Specifies information which will be displayed in the now playing notification.\n       * When undefined the player will display information contained in the video metadata.\n       * @platform android\n       * @platform ios\n       */\n      metadata?: VideoMetadata;\n\n      /**\n       * Specifies headers sent with the video request.\n       * > For DRM license headers use the `headers` field of [`DRMOptions`](#drmoptions).\n       * @platform android\n       * @platform ios\n       */\n      headers?: Record<string, string>;\n\n      /**\n       * Specifies whether the player should use caching for the video.\n       * > Due to platform limitations, the cache cannot be used with HLS video sources on iOS. Caching DRM-protected videos is not supported on Android and iOS.\n       * @default false\n       * @platform android\n       * @platform ios\n       */\n      useCaching?: boolean;\n\n      /**\n       * Specifies the content type of the video source. When set to `'auto'`, the player will try to automatically determine the content type.\n       *\n       * You should use this property when playing HLS, SmoothStreaming or DASH videos from an uri, which does not contain a standardized extension for the corresponding media type.\n       * @default 'auto'\n       * @platform android\n       * @platform ios\n       */\n      contentType?: ContentType;\n    };\n\n/**\n * Contains information about any errors that the player encountered during the playback\n */\nexport type PlayerError = {\n  message: string;\n};\n\n/**\n * Contains information that will be displayed in the now playing notification when the video is playing.\n * @platform android\n * @platform ios\n */\nexport type VideoMetadata = {\n  /**\n   * The title of the video.\n   * @platform android\n   * @platform ios\n   */\n  title?: string;\n  /**\n   * Secondary text that will be displayed under the title.\n   * @platform android\n   * @platform ios\n   */\n  artist?: string;\n  /**\n   * The uri of the video artwork.\n   * @platform android\n   * @platform ios\n   */\n  artwork?: string;\n};\n\n/**\n * Specifies which type of DRM to use:\n * - Android supports ClearKey, PlayReady and Widevine.\n * - iOS supports FairPlay.\n */\nexport type DRMType = 'clearkey' | 'fairplay' | 'playready' | 'widevine';\n\n/**\n * Specifies DRM options which will be used by the player while loading the video.\n */\nexport type DRMOptions = {\n  /**\n   * Determines which type of DRM to use.\n   */\n  type: DRMType;\n\n  /**\n   * Determines the license server URL.\n   */\n  licenseServer: string;\n\n  /**\n   * Determines headers sent to the license server on license requests.\n   */\n  headers?: Record<string, string>;\n\n  /**\n   * Specifies whether the DRM is a multi-key DRM.\n   * @platform android\n   */\n  multiKey?: boolean;\n\n  /**\n   * Specifies the content ID of the stream.\n   * @platform ios\n   */\n  contentId?: string;\n\n  /**\n   * Specifies the certificate URL for the FairPlay DRM.\n   * @platform ios\n   */\n  certificateUrl?: string;\n\n  /**\n   * Specifies the base64 encoded certificate data for the FairPlay DRM.\n   * When this property is set, the `certificateUrl` property is ignored.\n   * @platform ios\n   */\n  base64CertificateData?: string;\n};\n\n/**\n * Specifies buffer options which will be used by the player when buffering the video.\n *\n * @platform android\n * @platform ios\n */\nexport type BufferOptions = {\n  /**\n   * The duration in seconds which determines how much media the player should buffer ahead of the current playback time.\n   *\n   * On iOS when set to `0` the player will automatically decide appropriate buffer duration.\n   *\n   * Equivalent to [`AVPlayerItem.preferredForwardBufferDuration`](https://developer.apple.com/documentation/avfoundation/avplayeritem/1643630-preferredforwardbufferduration).\n   * @default Android: 20, iOS: 0\n   * @platform android\n   * @platform ios\n   */\n  readonly preferredForwardBufferDuration?: number;\n\n  /**\n   * A Boolean value that indicates whether the player should automatically delay playback in order to minimize stalling.\n   *\n   * Equivalent to [`AVPlayer.automaticallyWaitsToMinimizeStalling`](https://developer.apple.com/documentation/avfoundation/avplayer/1643482-automaticallywaitstominimizestal).\n   * @default true\n   * @platform ios\n   */\n  readonly waitsToMinimizeStalling?: boolean;\n\n  /**\n   * Minimum duration of the buffer in seconds required to continue playing after the player has been paused or started buffering.\n   *\n   * > This property will be ignored if `preferredForwardBufferDuration` is lower.\n   * @default 2\n   * @platform android\n   */\n  readonly minBufferForPlayback?: number;\n\n  /**\n   * The maximum number of bytes that the player can buffer from the network.\n   * When 0 the player will automatically decide appropriate buffer size.\n   *\n   * @default 0\n   * @platform android\n   */\n  readonly maxBufferBytes?: number | null;\n\n  /**\n   * A Boolean value which determines whether the player should prioritize time over size when buffering media.\n   *\n   * @default false\n   * @platform android\n   */\n  readonly prioritizeTimeOverSizeThreshold?: boolean;\n};\n\n/**\n * Specifies the content type of the source.\n *\n * - `auto`: The player will automatically determine the content type of the video.\n * - `progressive`: The player will use progressive download content type. This is the default `ContentType` when the uri does not contain an extension.\n * - `hls`: The player will use HLS content type.\n * - `dash`: The player will use DASH content type (Android-only).\n * - `smoothStreaming`: The player will use SmoothStreaming content type (Android-only).\n *\n * @default `auto`\n */\nexport type ContentType = 'auto' | 'progressive' | 'hls' | 'dash' | 'smoothStreaming';\n\n/**\n * Specifies the audio mode that the player should use. Audio mode is set on per-app basis, if there are multiple players playing and\n * have different a `AudioMode` specified, the highest priority mode will be used. Priority order: 'doNotMix' > 'auto' > 'duckOthers' > 'mixWithOthers'.\n *\n * - `mixWithOthers`: The player will mix its audio output with other apps.\n * - `duckOthers`: The player will lower the volume of other apps if any of the active players is outputting audio.\n * - `auto`: The player will allow other apps to keep playing audio only when it is muted. On iOS it will always interrupt other apps when `showNowPlayingNotification` is `true` due to system requirements.\n * - `doNotMix`: The player will pause playback in other apps, even when it's muted.\n *\n * > On iOS, the Now Playing notification is dependent on the audio mode. If the audio mode is different from `doNotMix` or `auto` this feature will not work.\n */\nexport type AudioMixingMode = 'mixWithOthers' | 'duckOthers' | 'auto' | 'doNotMix';\n\nexport type SubtitleTrack = {\n  /**\n   * A string used by `expo-video` to identify the subtitle track.\n   *\n   * @platform android\n   */\n  id: string;\n\n  /**\n   * Language of the subtitle track. For example, `en`, `pl`, `de`.\n   */\n  language: string;\n\n  /**\n   * Label of the subtitle track in the language of the device.\n   */\n  label: string;\n};\n\n/**\n * Specifies a VideoTrack loaded from a [`VideoSource`](#videosource).\n */\nexport type VideoTrack = {\n  /**\n   * The id of the video track.\n   *\n   * > This field is platform-specific and may return different depending on the operating system.\n   */\n  id: string;\n\n  /**\n   * Size of the video track.\n   */\n  size: VideoSize;\n\n  /**\n   * MimeType of the video track or null if unknown.\n   */\n  mimeType: string | null;\n\n  /**\n   * Indicates whether the video track format is supported by the device.\n   *\n   * @platform android\n   */\n  isSupported: boolean;\n\n  /**\n   * Specifies the bitrate in bits per second. This is the peak bitrate if known, or else the average bitrate if known, or else null.\n   */\n  bitrate: number | null;\n\n  /**\n   * Specifies the frame rate of the video track in frames per second.\n   */\n  frameRate: number | null;\n};\n\n/**\n * Specifies the size of a video track.\n */\nexport type VideoSize = {\n  /**\n   * Width of the video track in pixels.\n   */\n  width: number;\n  /**\n   * Height of the video track in pixels.\n   */\n  height: number;\n};\n\nexport type AudioTrack = {\n  /**\n   * A string used by expo-video to identify the audio track.\n   * @platform android\n   */\n  id: string;\n\n  /**\n   * Language of the audio track. For example, 'en', 'pl', 'de'.\n   */\n  language: string;\n\n  /**\n   * Label of the audio track in the language of the device.\n   */\n  label: string;\n};\n"]}