import * as Crypto from 'expo-crypto';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Security configuration
const ENCRYPTION_SECRET = process.env.ENCRYPTION_SECRET || '';
const RECAPTCHA_SITE_KEY = process.env.EXPO_PUBLIC_RECAPTCHA_SITE_KEY || '';

/**
 * Encrypt sensitive data before storing
 */
export const encryptData = async (data: string): Promise<string> => {
  try {
    if (!ENCRYPTION_SECRET) {
      console.warn('⚠️ Encryption secret not configured, storing data as-is');
      return data;
    }

    // Simple encryption using crypto digest (for demo purposes)
    // In production, use proper encryption libraries like crypto-js
    const encrypted = await Crypto.digestStringAsync(
      Crypto.CryptoDigestAlgorithm.SHA256,
      data + ENCRYPTION_SECRET
    );
    
    return encrypted;
  } catch (error) {
    console.error('Encryption error:', error);
    return data; // Fallback to unencrypted
  }
};

/**
 * Decrypt sensitive data after retrieving
 */
export const decryptData = async (encryptedData: string): Promise<string> => {
  try {
    if (!ENCRYPTION_SECRET) {
      return encryptedData;
    }

    // For demo purposes, we'll just return the encrypted data
    // In production, implement proper decryption
    return encryptedData;
  } catch (error) {
    console.error('Decryption error:', error);
    return encryptedData;
  }
};

/**
 * Securely store sensitive data
 */
export const secureStore = async (key: string, value: string): Promise<void> => {
  try {
    const encryptedValue = await encryptData(value);
    await AsyncStorage.setItem(`secure_${key}`, encryptedValue);
  } catch (error) {
    console.error('Secure store error:', error);
    throw new Error('Failed to securely store data');
  }
};

/**
 * Securely retrieve sensitive data
 */
export const secureRetrieve = async (key: string): Promise<string | null> => {
  try {
    const encryptedValue = await AsyncStorage.getItem(`secure_${key}`);
    if (!encryptedValue) return null;
    
    return await decryptData(encryptedValue);
  } catch (error) {
    console.error('Secure retrieve error:', error);
    return null;
  }
};

/**
 * Generate secure random string
 */
export const generateSecureRandom = async (length: number = 32): Promise<string> => {
  try {
    const randomBytes = await Crypto.getRandomBytesAsync(length);
    return Array.from(randomBytes, byte => byte.toString(16).padStart(2, '0')).join('');
  } catch (error) {
    console.error('Random generation error:', error);
    // Fallback to timestamp + random
    return Date.now().toString() + Math.random().toString(36).substr(2);
  }
};

/**
 * Hash password or sensitive strings
 */
export const hashString = async (input: string, salt?: string): Promise<string> => {
  try {
    const saltToUse = salt || await generateSecureRandom(16);
    const hashed = await Crypto.digestStringAsync(
      Crypto.CryptoDigestAlgorithm.SHA256,
      input + saltToUse
    );
    
    return `${saltToUse}:${hashed}`;
  } catch (error) {
    console.error('Hash error:', error);
    throw new Error('Failed to hash string');
  }
};

/**
 * Verify hashed string
 */
export const verifyHash = async (input: string, hashedValue: string): Promise<boolean> => {
  try {
    const [salt, hash] = hashedValue.split(':');
    if (!salt || !hash) return false;
    
    const inputHash = await Crypto.digestStringAsync(
      Crypto.CryptoDigestAlgorithm.SHA256,
      input + salt
    );
    
    return inputHash === hash;
  } catch (error) {
    console.error('Hash verification error:', error);
    return false;
  }
};

/**
 * Validate email format
 */
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validate password strength
 */
export const validatePassword = (password: string): {
  isValid: boolean;
  errors: string[];
  strength: 'weak' | 'medium' | 'strong';
} => {
  const errors: string[] = [];
  let score = 0;

  // Length check
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  } else {
    score += 1;
  }

  // Uppercase check
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  } else {
    score += 1;
  }

  // Lowercase check
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  } else {
    score += 1;
  }

  // Number check
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  } else {
    score += 1;
  }

  // Special character check
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character');
  } else {
    score += 1;
  }

  // Determine strength
  let strength: 'weak' | 'medium' | 'strong' = 'weak';
  if (score >= 4) strength = 'strong';
  else if (score >= 3) strength = 'medium';

  return {
    isValid: errors.length === 0,
    errors,
    strength,
  };
};

/**
 * Sanitize user input to prevent XSS
 */
export const sanitizeInput = (input: string): string => {
  return input
    .replace(/[<>]/g, '') // Remove < and >
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
};

/**
 * Generate device fingerprint for security
 */
export const generateDeviceFingerprint = async (): Promise<string> => {
  try {
    // Collect device information
    const deviceInfo = {
      platform: 'mobile',
      timestamp: Date.now(),
      random: await generateSecureRandom(8),
    };

    const fingerprint = await Crypto.digestStringAsync(
      Crypto.CryptoDigestAlgorithm.SHA256,
      JSON.stringify(deviceInfo)
    );

    return fingerprint;
  } catch (error) {
    console.error('Device fingerprint error:', error);
    return 'unknown_device';
  }
};

/**
 * Rate limiting helper
 */
class RateLimiter {
  private attempts: Map<string, number[]> = new Map();
  private maxAttempts: number;
  private windowMs: number;

  constructor(maxAttempts: number = 5, windowMs: number = 15 * 60 * 1000) {
    this.maxAttempts = maxAttempts;
    this.windowMs = windowMs;
  }

  isAllowed(identifier: string): boolean {
    const now = Date.now();
    const attempts = this.attempts.get(identifier) || [];
    
    // Remove old attempts outside the window
    const validAttempts = attempts.filter(time => now - time < this.windowMs);
    
    if (validAttempts.length >= this.maxAttempts) {
      return false;
    }

    // Add current attempt
    validAttempts.push(now);
    this.attempts.set(identifier, validAttempts);
    
    return true;
  }

  getRemainingTime(identifier: string): number {
    const attempts = this.attempts.get(identifier) || [];
    if (attempts.length === 0) return 0;
    
    const oldestAttempt = Math.min(...attempts);
    const remainingTime = this.windowMs - (Date.now() - oldestAttempt);
    
    return Math.max(0, remainingTime);
  }

  reset(identifier: string): void {
    this.attempts.delete(identifier);
  }
}

// Export rate limiter instances
export const loginRateLimiter = new RateLimiter(5, 15 * 60 * 1000); // 5 attempts per 15 minutes
export const apiRateLimiter = new RateLimiter(100, 60 * 1000); // 100 requests per minute

/**
 * Security headers for API requests
 */
export const getSecurityHeaders = async (): Promise<Record<string, string>> => {
  const deviceFingerprint = await generateDeviceFingerprint();
  
  return {
    'X-Device-Fingerprint': deviceFingerprint,
    'X-Requested-With': 'TribeLabMobile',
    'X-Client-Version': '1.0.0',
  };
};

/**
 * Validate reCAPTCHA token (for forms)
 */
export const validateRecaptcha = async (token: string): Promise<boolean> => {
  try {
    if (!RECAPTCHA_SITE_KEY) {
      console.warn('⚠️ reCAPTCHA not configured');
      return true; // Skip validation if not configured
    }

    // In a real implementation, you would verify the token with Google's API
    // For now, we'll just check if token exists
    return token && token.length > 0;
  } catch (error) {
    console.error('reCAPTCHA validation error:', error);
    return false;
  }
};

/**
 * Security utilities object
 */
export const Security = {
  encrypt: encryptData,
  decrypt: decryptData,
  store: secureStore,
  retrieve: secureRetrieve,
  generateRandom: generateSecureRandom,
  hash: hashString,
  verify: verifyHash,
  validateEmail,
  validatePassword,
  sanitize: sanitizeInput,
  fingerprint: generateDeviceFingerprint,
  headers: getSecurityHeaders,
  recaptcha: validateRecaptcha,
  rateLimiters: {
    login: loginRateLimiter,
    api: apiRateLimiter,
  },
};
