{"version": 3, "file": "NativeVideoView.js", "sourceRoot": "", "sources": ["../src/NativeVideoView.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,wBAAwB,EAAE,MAAM,mBAAmB,CAAC;AAC7D,OAAO,EAAE,QAAQ,EAAE,MAAM,cAAc,CAAC;AAExC,MAAM,eAAe,GAAG,QAAQ,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,WAAW,CAAC;AAErF,eAAe,wBAAwB,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;AACtE,MAAM,CAAC,MAAM,sBAAsB,GACjC,QAAQ,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,wBAAwB,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC", "sourcesContent": ["import { requireNativeViewManager } from 'expo-modules-core';\nimport { Platform } from 'react-native';\n\nconst defaultViewName = Platform.OS === 'android' ? 'SurfaceVideoView' : 'VideoView';\n\nexport default requireNativeViewManager('ExpoVideo', defaultViewName);\nexport const NativeTextureVideoView =\n  Platform.OS === 'android' ? requireNativeViewManager('ExpoVideo', 'TextureVideoView') : null;\n"]}