{"version": 3, "file": "VideoPlayer.web.js", "sourceRoot": "", "sources": ["../src/VideoPlayer.web.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,OAAO,CAAC;AAehC,OAAO,kBAAkB,MAAM,sBAAsB,CAAC;AAEtD,MAAM,UAAU,cAAc,CAC5B,MAAmB,EACnB,KAAqC;IAErC,MAAM,YAAY,GAAG,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC;IAE3E,OAAO,OAAO,CAAC,GAAG,EAAE;QAClB,MAAM,MAAM,GAAG,IAAI,cAAc,CAAC,YAAY,CAAC,CAAC;QAChD,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC;QAChB,OAAO,MAAM,CAAC;IAChB,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC/B,CAAC;AAED,MAAM,UAAU,YAAY,CAAC,MAAmB;IAC9C,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;QAC/B,OAAO,MAAM,CAAC;IAChB,CAAC;IACD,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;QAC/B,OAAO,kBAAkB,CAAC,MAAM,CAAC,EAAE,GAAG,IAAI,IAAI,CAAC;IACjD,CAAC;IACD,IAAI,OAAO,MAAM,EAAE,OAAO,KAAK,QAAQ,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC;QACxD,OAAO,kBAAkB,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,GAAG,IAAI,IAAI,CAAC;IACzD,CAAC;IAED,OAAO,MAAM,EAAE,GAAG,IAAI,IAAI,CAAC;AAC7B,CAAC;AAED,MAAM,UAAU,iBAAiB,CAAC,MAAmB;IACnD,MAAM,YAAY,GAAG,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC;IAE3E,OAAO,IAAI,cAAc,CAAC,YAAY,CAAC,CAAC;AAC1C,CAAC;AAED,MAAM,CAAC,OAAO,OAAO,cACnB,SAAQ,UAAU,CAAC,IAAI,CAAC,YAA+B;IAGvD,YAAY,MAAmB;QAC7B,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC;IACpB,CAAC;IAED,GAAG,GAAgB,IAAI,CAAC;IACxB,WAAW,GAAgB,IAAI,CAAC;IAChC,cAAc,GAA0B,IAAI,GAAG,EAAE,CAAC;IAClD,WAAW,GAAqC,IAAI,GAAG,EAAE,CAAC;IAC1D,OAAO,GAAY,KAAK,CAAC;IACzB,MAAM,GAAY,KAAK,CAAC;IACxB,OAAO,GAAW,CAAC,CAAC;IACpB,KAAK,GAAY,KAAK,CAAC;IACvB,aAAa,GAAW,GAAG,CAAC;IAC5B,eAAe,GAAY,IAAI,CAAC;IAChC,OAAO,GAAsB,MAAM,CAAC;IACpC,MAAM,GAAuB,IAAI,CAAC;IAClC,eAAe,GAAkB,IAAI,CAAC;IACtC,wBAAwB,GAAW,CAAC,CAAC;IACrC,eAAe,GAAoB,MAAM,CAAC,CAAC,sDAAsD;IACjG,sBAAsB,GAAY,KAAK,CAAC,CAAC,sDAAsD;IAC/F,uBAAuB,GAAY,KAAK,CAAC,CAAC,sDAAsD;IAChG,0BAA0B,GAAY,KAAK,CAAC,CAAC,sDAAsD;IACnG,oBAAoB,GAAkB,IAAI,CAAC,CAAC,sDAAsD;IAClG,qBAAqB,GAAkB,IAAI,CAAC,CAAC,sDAAsD;IACnG,oBAAoB,GAAW,CAAC,CAAC,CAAC,sDAAsD;IACxF,aAAa,GAAkB,EAAmB,CAAC,CAAC,sDAAsD;IAC1G,aAAa,GAAyB,IAAI,CAAC,CAAC,6FAA6F;IACzI,uBAAuB,GAAoB,EAAE,CAAC,CAAC,6FAA6F;IAC5I,UAAU,GAAsB,IAAI,CAAC,CAAC,sDAAsD;IAC5F,oBAAoB,GAAiB,EAAE,CAAC,CAAC,sDAAsD;IAC/F,UAAU,GAAsB,IAAI,CAAC,CAAC,sDAAsD;IAC5F,oBAAoB,GAAiB,EAAE,CAAC,CAAC,sDAAsD;IAE/F,IAAI,KAAK,CAAC,KAAc;QACtB,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YACpC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;QACtB,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACtB,CAAC;IAED,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED,IAAI,YAAY,CAAC,KAAa;QAC5B,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YACpC,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC;QAC7B,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;IAC7B,CAAC;IAED,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;IAED,IAAI,MAAM;QACR,OAAO,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,KAAK,QAAQ,CAAC;IAC5D,CAAC;IAED,IAAI,MAAM,CAAC,KAAa;QACtB,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YACpC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC;QACvB,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;IACvB,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED,IAAI,IAAI,CAAC,KAAc;QACrB,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YACpC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC;QACrB,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACrB,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED,IAAI,WAAW;QACb,mFAAmF;QACnF,OAAO,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,IAAI,CAAC,CAAC;IACvD,CAAC;IAED,IAAI,WAAW,CAAC,KAAa;QAC3B,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YACpC,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC;QAC5B,CAAC,CAAC,CAAC;IACL,CAAC;IAED,IAAI,QAAQ;QACV,0FAA0F;QAC1F,OAAO,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,IAAI,CAAC,CAAC;IACpD,CAAC;IAED,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED,IAAI,cAAc,CAAC,KAAc;QAC/B,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YACpC,KAAK,CAAC,cAAc,GAAG,KAAK,CAAC;QAC/B,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;IAC/B,CAAC;IAED,IAAI,uBAAuB;QACzB,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACvC,CAAC;IACD,IAAI,uBAAuB,CAAC,KAAa;QACvC,IAAI,CAAC,wBAAwB,GAAG,KAAK,CAAC;QACtC,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACtC,CAAC;QACD,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;YACd,2DAA2D;YAC3D,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;gBACtB,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,oBAAoB,EAAE,IAAI;gBAC1B,qBAAqB,EAAE,IAAI;gBAC3B,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;aACxC,CAAC,CAAC;YAEH,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC,GAAG,EAAE;gBACtC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;oBACtB,WAAW,EAAE,IAAI,CAAC,WAAW;oBAC7B,oBAAoB,EAAE,IAAI;oBAC1B,qBAAqB,EAAE,IAAI;oBAC3B,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;iBACxC,CAAC,CAAC;YACL,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,CAAC;QACnB,CAAC;IACH,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED,IAAI,gBAAgB;QAClB,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,KAAK,OAAO,EAAE,CAAC;YAC9D,OAAO,CAAC,CAAC,CAAC;QACZ,CAAC;QACD,MAAM,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC;QACvD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACzC,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,WAAW,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjF,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;QACD,OAAO,CAAC,CAAC;IACX,CAAC;IAED,IAAY,MAAM,CAAC,KAAwB;QACzC,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK;YAAE,OAAO;QAEnC,IAAI,KAAK,KAAK,OAAO,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACrC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBACxB,MAAM,EAAE,KAAK;gBACb,SAAS,EAAE,IAAI,CAAC,OAAO;gBACvB,KAAK,EAAE,IAAI,CAAC,MAAM;aACnB,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBACxB,MAAM,EAAE,KAAK;gBACb,SAAS,EAAE,IAAI,CAAC,OAAO;aACxB,CAAC,CAAC;YACH,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACrB,CAAC;QACD,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;IACvB,CAAC;IAED,cAAc,CAAC,KAAuB;QACpC,kGAAkG;QAClG,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YACnC,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC;YAC5C,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;YACxB,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;YAC5B,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;YAC1B,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC;QAC1C,CAAC;QACD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAC/B,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC1B,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC;IACzC,CAAC;IAED,gBAAgB,CAAC,KAAuB;QACtC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACpC,CAAC;IAED,cAAc,CACZ,YAA0B,EAC1B,YAAsB,EACtB,eAA4C;QAE5C,IAAI,CAAC,YAAY,IAAI,CAAC,YAAY;YAAE,OAAO;QAE3C,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QACtC,mGAAmG;QACnG,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YAChC,eAAe,CAAC,OAAO,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;QACpD,CAAC;aAAM,CAAC;YACN,eAAe,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QACxC,CAAC;IACH,CAAC;IAED,gBAAgB,CACd,KAAuB,EACvB,YAA0B,EAC1B,eAA4C;QAE5C,MAAM,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC;QAC/C,MAAM,iBAAiB,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;QAC3C,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;QACzC,eAAe,CAAC,UAAU,EAAE,CAAC;QAE7B,6HAA6H;QAC7H,IAAI,iBAAiB,KAAK,KAAK,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,CAAC,IAAI,YAAY,EAAE,CAAC;YAC7E,MAAM,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;YACpD,kBAAkB,CAAC,UAAU,EAAE,CAAC;YAChC,kBAAkB,CAAC,OAAO,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAED,IAAI;QACF,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YACpC,KAAK,CAAC,IAAI,EAAE,CAAC;QACf,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK;QACH,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YACpC,KAAK,CAAC,KAAK,EAAE,CAAC;QAChB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,OAAO,CAAC,MAAmB;QACzB,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YACpC,MAAM,GAAG,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;YACjC,KAAK,CAAC,KAAK,EAAE,CAAC;YACd,IAAI,GAAG,EAAE,CAAC;gBACR,KAAK,CAAC,YAAY,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;gBAC/B,KAAK,CAAC,IAAI,EAAE,CAAC;gBACb,KAAK,CAAC,IAAI,EAAE,CAAC;YACf,CAAC;iBAAM,CAAC;gBACN,KAAK,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;gBAC7B,KAAK,CAAC,IAAI,EAAE,CAAC;YACf,CAAC;QACH,CAAC,CAAC,CAAC;QACH,oEAAoE;QACpE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC;QAC5B,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC;QAClB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACtB,CAAC;IAED,yGAAyG;IACzG,4DAA4D;IAC5D,KAAK,CAAC,YAAY,CAAC,MAAmB;QACpC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAC9B,CAAC;IAED,MAAM,CAAC,OAAe;QACpB,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YACpC,KAAK,CAAC,WAAW,IAAI,OAAO,CAAC;QAC/B,CAAC,CAAC,CAAC;IACL,CAAC;IAED,MAAM;QACJ,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YACpC,KAAK,CAAC,WAAW,GAAG,CAAC,CAAC;YACtB,KAAK,CAAC,IAAI,EAAE,CAAC;QACf,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACtB,CAAC;IAED,uBAAuB,CAAC,KAAwB;QAC9C,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;IAC7E,CAAC;IAED,0BAA0B,CAAC,KAAuB;QAChD,MAAM,UAAU,GAAG,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,IAAI,CAAC,UAAU;YAAE,OAAO;QAExB,IAAI,UAAU,CAAC,MAAM,EAAE,CAAC;YACtB,KAAK,CAAC,KAAK,EAAE,CAAC;QAChB,CAAC;aAAM,CAAC;YACN,KAAK,CAAC,IAAI,EAAE,CAAC;QACf,CAAC;QACD,KAAK,CAAC,WAAW,GAAG,UAAU,CAAC,WAAW,CAAC;QAC3C,KAAK,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;QACjC,KAAK,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC;QAC/B,KAAK,CAAC,YAAY,GAAG,UAAU,CAAC,YAAY,CAAC;IAC/C,CAAC;IAED;;;OAGG;IACH,SAAS,CACP,WAA6B,EAC7B,SAAoB,EACpB,GAAG,IAA8C;QAEjD,MAAM,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC;QAC/C,IAAI,aAAa,CAAC,CAAC,CAAC,KAAK,WAAW,EAAE,CAAC;YACrC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,CAAC;QAChC,CAAC;IACH,CAAC;IAED,aAAa,CAAC,KAAuB;QACnC,KAAK,CAAC,MAAM,GAAG,GAAG,EAAE;YAClB,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,eAAe,EAAE;gBACrC,SAAS,EAAE,IAAI;gBACf,YAAY,EAAE,IAAI,CAAC,OAAO;aAC3B,CAAC,CAAC;YACH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YACpB,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,EAAE;gBAC3C,YAAY,CAAC,IAAI,EAAE,CAAC;YACtB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;QAEF,KAAK,CAAC,OAAO,GAAG,GAAG,EAAE;YACnB,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,eAAe,EAAE;gBACrC,SAAS,EAAE,KAAK;gBAChB,YAAY,EAAE,IAAI,CAAC,OAAO;aAC3B,CAAC,CAAC;YACH,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;YACrB,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,EAAE;gBAC3C,YAAY,CAAC,KAAK,EAAE,CAAC;YACvB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;QAEF,KAAK,CAAC,cAAc,GAAG,GAAG,EAAE;YAC1B,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,cAAc,EAAE,EAAE,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;YACxF,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,aAAa,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;YACnF,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YAC3B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;QAC3B,CAAC,CAAC;QAEF,KAAK,CAAC,SAAS,GAAG,GAAG,EAAE;YACrB,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,EAAE;gBAC3C,IAAI,YAAY,KAAK,KAAK,IAAI,YAAY,CAAC,WAAW,KAAK,KAAK,CAAC,WAAW;oBAAE,OAAO;gBACrF,YAAY,CAAC,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC;YAC/C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;QAEF,KAAK,CAAC,QAAQ,GAAG,GAAG,EAAE;YACpB,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,EAAE;gBAC3C,IAAI,YAAY,KAAK,KAAK,IAAI,YAAY,CAAC,WAAW,KAAK,KAAK,CAAC,WAAW;oBAAE,OAAO;gBACrF,YAAY,CAAC,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC;YAC/C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;QAEF,KAAK,CAAC,YAAY,GAAG,GAAG,EAAE;YACxB,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,oBAAoB,EAAE;gBAC1C,YAAY,EAAE,KAAK,CAAC,YAAY;gBAChC,eAAe,EAAE,IAAI,CAAC,YAAY;aACnC,CAAC,CAAC;YACH,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,EAAE;gBAC3C,IAAI,YAAY,CAAC,YAAY,KAAK,KAAK,CAAC,YAAY;oBAAE,OAAO;gBAC7D,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,YAAY,CAAC;gBACxC,YAAY,CAAC,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC;YACjD,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,YAAY,CAAC;QAC1C,CAAC,CAAC;QAEF,KAAK,CAAC,OAAO,GAAG,GAAG,EAAE;YACnB,IAAI,CAAC,MAAM,GAAG;gBACZ,OAAO,EAAE,KAAK,CAAC,KAAK,EAAE,OAAO,IAAI,sBAAsB;aACxD,CAAC;YACF,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC;QACxB,CAAC,CAAC;QAEF,KAAK,CAAC,SAAS,GAAG,GAAG,EAAE;YACrB,MAAM,UAAU,GAAG,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE;gBAC1E,OAAO,aAAa,IAAI,KAAK,CAAC,UAAU,IAAI,CAAC,CAAC;YAChD,CAAC,EAAE,IAAI,CAAC,CAAC;YACT,IAAI,CAAC,UAAU;gBAAE,OAAO;YAExB,IAAI,CAAC,MAAM,GAAG,aAAa,CAAC;QAC9B,CAAC,CAAC;QAEF,KAAK,CAAC,SAAS,GAAG,GAAG,EAAE;YACrB,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS;gBAAE,OAAO;YACvC,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;QAC1B,CAAC,CAAC;QAEF,KAAK,CAAC,OAAO,GAAG,GAAG,EAAE;YACnB,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;QACrC,CAAC,CAAC;QAEF,KAAK,CAAC,WAAW,GAAG,GAAG,EAAE;YACvB,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,cAAc,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;QAC3F,CAAC,CAAC;IACJ,CAAC;CACF", "sourcesContent": ["import { useMemo } from 'react';\n\nimport type {\n  <PERSON><PERSON><PERSON><PERSON><PERSON>s,\n  PlayerError,\n  VideoPlayerStatus,\n  VideoSource,\n  VideoPlayer,\n  SubtitleTrack,\n  AudioMixingMode,\n  VideoTrack,\n  AudioTrack,\n} from './VideoPlayer.types';\nimport type { VideoPlayerEvents } from './VideoPlayerEvents.types';\nimport { VideoThumbnail } from './VideoThumbnail';\nimport resolveAssetSource from './resolveAssetSource';\n\nexport function useVideoPlayer(\n  source: VideoSource,\n  setup?: (player: VideoPlayer) => void\n): VideoPlayer {\n  const parsedSource = typeof source === 'string' ? { uri: source } : source;\n\n  return useMemo(() => {\n    const player = new VideoPlayerWeb(parsedSource);\n    setup?.(player);\n    return player;\n  }, [JSON.stringify(source)]);\n}\n\nexport function getSourceUri(source: VideoSource): string | null {\n  if (typeof source === 'string') {\n    return source;\n  }\n  if (typeof source === 'number') {\n    return resolveAssetSource(source)?.uri ?? null;\n  }\n  if (typeof source?.assetId === 'number' && !source?.uri) {\n    return resolveAssetSource(source.assetId)?.uri ?? null;\n  }\n\n  return source?.uri ?? null;\n}\n\nexport function createVideoPlayer(source: VideoSource): VideoPlayer {\n  const parsedSource = typeof source === 'string' ? { uri: source } : source;\n\n  return new VideoPlayerWeb(parsedSource);\n}\n\nexport default class VideoPlayerWeb\n  extends globalThis.expo.SharedObject<VideoPlayerEvents>\n  implements VideoPlayer\n{\n  constructor(source: VideoSource) {\n    super();\n    this.src = source;\n  }\n\n  src: VideoSource = null;\n  previousSrc: VideoSource = null;\n  _mountedVideos: Set<HTMLVideoElement> = new Set();\n  _audioNodes: Set<MediaElementAudioSourceNode> = new Set();\n  playing: boolean = false;\n  _muted: boolean = false;\n  _volume: number = 1;\n  _loop: boolean = false;\n  _playbackRate: number = 1.0;\n  _preservesPitch: boolean = true;\n  _status: VideoPlayerStatus = 'idle';\n  _error: PlayerError | null = null;\n  _timeUpdateLoop: number | null = null;\n  _timeUpdateEventInterval: number = 0;\n  audioMixingMode: AudioMixingMode = 'auto'; // Not supported on web. Dummy to match the interface.\n  allowsExternalPlayback: boolean = false; // Not supported on web. Dummy to match the interface.\n  staysActiveInBackground: boolean = false; // Not supported on web. Dummy to match the interface.\n  showNowPlayingNotification: boolean = false; // Not supported on web. Dummy to match the interface.\n  currentLiveTimestamp: number | null = null; // Not supported on web. Dummy to match the interface.\n  currentOffsetFromLive: number | null = null; // Not supported on web. Dummy to match the interface.\n  targetOffsetFromLive: number = 0; // Not supported on web. Dummy to match the interface.\n  bufferOptions: BufferOptions = {} as BufferOptions; // Not supported on web. Dummy to match the interface.\n  subtitleTrack: SubtitleTrack | null = null; // Embedded subtitles are not supported by the html web player. Dummy to match the interface.\n  availableSubtitleTracks: SubtitleTrack[] = []; // Embedded subtitles are not supported by the html web player. Dummy to match the interface.\n  audioTrack: AudioTrack | null = null; // Not supported on web. Dummy to match the interface.\n  availableAudioTracks: AudioTrack[] = []; // Not supported on web. Dummy to match the interface.\n  videoTrack: VideoTrack | null = null; // Not supported on web. Dummy to match the interface.\n  availableVideoTracks: VideoTrack[] = []; // Not supported on web. Dummy to match the interface.\n\n  set muted(value: boolean) {\n    this._mountedVideos.forEach((video) => {\n      video.muted = value;\n    });\n    this._muted = value;\n  }\n\n  get muted(): boolean {\n    return this._muted;\n  }\n\n  set playbackRate(value: number) {\n    this._mountedVideos.forEach((video) => {\n      video.playbackRate = value;\n    });\n    this._playbackRate = value;\n  }\n\n  get playbackRate(): number {\n    return this._playbackRate;\n  }\n\n  get isLive(): boolean {\n    return [...this._mountedVideos][0]?.duration === Infinity;\n  }\n\n  set volume(value: number) {\n    this._mountedVideos.forEach((video) => {\n      video.volume = value;\n    });\n    this._volume = value;\n  }\n\n  get volume(): number {\n    return this._volume;\n  }\n\n  set loop(value: boolean) {\n    this._mountedVideos.forEach((video) => {\n      video.loop = value;\n    });\n    this._loop = value;\n  }\n\n  get loop(): boolean {\n    return this._loop;\n  }\n\n  get currentTime(): number {\n    // All videos should be synchronized, so we return the position of the first video.\n    return [...this._mountedVideos][0]?.currentTime ?? 0;\n  }\n\n  set currentTime(value: number) {\n    this._mountedVideos.forEach((video) => {\n      video.currentTime = value;\n    });\n  }\n\n  get duration(): number {\n    // All videos should have the same duration, so we return the duration of the first video.\n    return [...this._mountedVideos][0]?.duration ?? 0;\n  }\n\n  get preservesPitch(): boolean {\n    return this._preservesPitch;\n  }\n\n  set preservesPitch(value: boolean) {\n    this._mountedVideos.forEach((video) => {\n      video.preservesPitch = value;\n    });\n    this._preservesPitch = value;\n  }\n\n  get timeUpdateEventInterval(): number {\n    return this._timeUpdateEventInterval;\n  }\n  set timeUpdateEventInterval(value: number) {\n    this._timeUpdateEventInterval = value;\n    if (this._timeUpdateLoop) {\n      clearInterval(this._timeUpdateLoop);\n    }\n    if (value > 0) {\n      // Emit the first event immediately like on other platforms\n      this.emit('timeUpdate', {\n        currentTime: this.currentTime,\n        currentLiveTimestamp: null,\n        currentOffsetFromLive: null,\n        bufferedPosition: this.bufferedPosition,\n      });\n\n      this._timeUpdateLoop = setInterval(() => {\n        this.emit('timeUpdate', {\n          currentTime: this.currentTime,\n          currentLiveTimestamp: null,\n          currentOffsetFromLive: null,\n          bufferedPosition: this.bufferedPosition,\n        });\n      }, value * 1000);\n    }\n  }\n\n  get status(): VideoPlayerStatus {\n    return this._status;\n  }\n\n  get bufferedPosition(): number {\n    if (this._mountedVideos.size === 0 || this.status === 'error') {\n      return -1;\n    }\n    const buffered = [...this._mountedVideos][0]?.buffered;\n    for (let i = 0; i < buffered.length; i++) {\n      if (buffered.start(i) <= this.currentTime && buffered.end(i) >= this.currentTime) {\n        return buffered.end(i);\n      }\n    }\n    return 0;\n  }\n\n  private set status(value: VideoPlayerStatus) {\n    if (this._status === value) return;\n\n    if (value === 'error' && this._error) {\n      this.emit('statusChange', {\n        status: value,\n        oldStatus: this._status,\n        error: this._error,\n      });\n    } else {\n      this.emit('statusChange', {\n        status: value,\n        oldStatus: this._status,\n      });\n      this._error = null;\n    }\n    this._status = value;\n  }\n\n  mountVideoView(video: HTMLVideoElement) {\n    // The video will be the first video, it should inherit the properties set in the setup() function\n    if (this._mountedVideos.size === 0) {\n      video.preservesPitch = this._preservesPitch;\n      video.loop = this._loop;\n      video.volume = this._volume;\n      video.muted = this._muted;\n      video.playbackRate = this._playbackRate;\n    }\n    this._mountedVideos.add(video);\n    this._addListeners(video);\n    this._synchronizeWithFirstVideo(video);\n  }\n\n  unmountVideoView(video: HTMLVideoElement) {\n    this._mountedVideos.delete(video);\n  }\n\n  mountAudioNode(\n    audioContext: AudioContext,\n    zeroGainNode: GainNode,\n    audioSourceNode: MediaElementAudioSourceNode\n  ): void {\n    if (!audioContext || !zeroGainNode) return;\n\n    this._audioNodes.add(audioSourceNode);\n    // First mounted video should be connected to the audio context. All other videos have to be muted.\n    if (this._audioNodes.size === 1) {\n      audioSourceNode.connect(audioContext.destination);\n    } else {\n      audioSourceNode.connect(zeroGainNode);\n    }\n  }\n\n  unmountAudioNode(\n    video: HTMLVideoElement,\n    audioContext: AudioContext,\n    audioSourceNode: MediaElementAudioSourceNode\n  ) {\n    const mountedVideos = [...this._mountedVideos];\n    const videoPlayingAudio = mountedVideos[0];\n    this._audioNodes.delete(audioSourceNode);\n    audioSourceNode.disconnect();\n\n    // If video playing audio has been removed, select a new video to be the audio player by disconnecting it from the mute node.\n    if (videoPlayingAudio === video && this._audioNodes.size > 0 && audioContext) {\n      const newMainAudioSource = [...this._audioNodes][0];\n      newMainAudioSource.disconnect();\n      newMainAudioSource.connect(audioContext.destination);\n    }\n  }\n\n  play(): void {\n    this._mountedVideos.forEach((video) => {\n      video.play();\n    });\n  }\n\n  pause(): void {\n    this._mountedVideos.forEach((video) => {\n      video.pause();\n    });\n  }\n\n  replace(source: VideoSource): void {\n    this._mountedVideos.forEach((video) => {\n      const uri = getSourceUri(source);\n      video.pause();\n      if (uri) {\n        video.setAttribute('src', uri);\n        video.load();\n        video.play();\n      } else {\n        video.removeAttribute('src');\n        video.load();\n      }\n    });\n    // TODO @behenate: this won't work when we add support for playlists\n    this.previousSrc = this.src;\n    this.src = source;\n    this.playing = true;\n  }\n\n  // The HTML5 player already offloads loading of the asset onto a different thread so we can keep the same\n  // implementation until `replace` is deprecated and removed.\n  async replaceAsync(source: VideoSource): Promise<void> {\n    return this.replace(source);\n  }\n\n  seekBy(seconds: number): void {\n    this._mountedVideos.forEach((video) => {\n      video.currentTime += seconds;\n    });\n  }\n\n  replay(): void {\n    this._mountedVideos.forEach((video) => {\n      video.currentTime = 0;\n      video.play();\n    });\n    this.playing = true;\n  }\n\n  generateThumbnailsAsync(times: number | number[]): Promise<VideoThumbnail[]> {\n    throw new Error('Generating video thumbnails is not supported on Web yet');\n  }\n\n  _synchronizeWithFirstVideo(video: HTMLVideoElement): void {\n    const firstVideo = [...this._mountedVideos][0];\n    if (!firstVideo) return;\n\n    if (firstVideo.paused) {\n      video.pause();\n    } else {\n      video.play();\n    }\n    video.currentTime = firstVideo.currentTime;\n    video.volume = firstVideo.volume;\n    video.muted = firstVideo.muted;\n    video.playbackRate = firstVideo.playbackRate;\n  }\n\n  /**\n   * If there are multiple mounted videos, all of them will emit an event, as they are synchronised.\n   * We want to avoid this, so we only emit the event if it came from the first video.\n   */\n  _emitOnce<EventName extends keyof VideoPlayerEvents>(\n    eventSource: HTMLVideoElement,\n    eventName: EventName,\n    ...args: Parameters<VideoPlayerEvents[EventName]>\n  ): void {\n    const mountedVideos = [...this._mountedVideos];\n    if (mountedVideos[0] === eventSource) {\n      this.emit(eventName, ...args);\n    }\n  }\n\n  _addListeners(video: HTMLVideoElement): void {\n    video.onplay = () => {\n      this._emitOnce(video, 'playingChange', {\n        isPlaying: true,\n        oldIsPlaying: this.playing,\n      });\n      this.playing = true;\n      this._mountedVideos.forEach((mountedVideo) => {\n        mountedVideo.play();\n      });\n    };\n\n    video.onpause = () => {\n      this._emitOnce(video, 'playingChange', {\n        isPlaying: false,\n        oldIsPlaying: this.playing,\n      });\n      this.playing = false;\n      this._mountedVideos.forEach((mountedVideo) => {\n        mountedVideo.pause();\n      });\n    };\n\n    video.onvolumechange = () => {\n      this._emitOnce(video, 'volumeChange', { volume: video.volume, oldVolume: this.volume });\n      this._emitOnce(video, 'mutedChange', { muted: video.muted, oldMuted: this.muted });\n      this.volume = video.volume;\n      this.muted = video.muted;\n    };\n\n    video.onseeking = () => {\n      this._mountedVideos.forEach((mountedVideo) => {\n        if (mountedVideo === video || mountedVideo.currentTime === video.currentTime) return;\n        mountedVideo.currentTime = video.currentTime;\n      });\n    };\n\n    video.onseeked = () => {\n      this._mountedVideos.forEach((mountedVideo) => {\n        if (mountedVideo === video || mountedVideo.currentTime === video.currentTime) return;\n        mountedVideo.currentTime = video.currentTime;\n      });\n    };\n\n    video.onratechange = () => {\n      this._emitOnce(video, 'playbackRateChange', {\n        playbackRate: video.playbackRate,\n        oldPlaybackRate: this.playbackRate,\n      });\n      this._mountedVideos.forEach((mountedVideo) => {\n        if (mountedVideo.playbackRate === video.playbackRate) return;\n        this._playbackRate = video.playbackRate;\n        mountedVideo.playbackRate = video.playbackRate;\n      });\n      this._playbackRate = video.playbackRate;\n    };\n\n    video.onerror = () => {\n      this._error = {\n        message: video.error?.message ?? 'Unknown player error',\n      };\n      this.status = 'error';\n    };\n\n    video.oncanplay = () => {\n      const allCanPlay = [...this._mountedVideos].reduce((previousValue, video) => {\n        return previousValue && video.readyState >= 3;\n      }, true);\n      if (!allCanPlay) return;\n\n      this.status = 'readyToPlay';\n    };\n\n    video.onwaiting = () => {\n      if (this._status === 'loading') return;\n      this.status = 'loading';\n    };\n\n    video.onended = () => {\n      this._emitOnce(video, 'playToEnd');\n    };\n\n    video.onloadstart = () => {\n      this._emitOnce(video, 'sourceChange', { source: this.src, oldSource: this.previousSrc });\n    };\n  }\n}\n"]}