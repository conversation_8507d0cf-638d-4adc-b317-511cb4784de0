{"version": 3, "file": "VideoView.js", "sourceRoot": "", "sources": ["../src/VideoView.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAa,aAAa,EAAE,SAAS,EAAE,MAAM,OAAO,CAAC;AAE5D,OAAO,iBAAiB,MAAM,qBAAqB,CAAC;AACpD,OAAO,eAAe,EAAE,EAAE,sBAAsB,EAAE,MAAM,mBAAmB,CAAC;AAI5E;;;;;;;;;GASG;AACH,MAAM,UAAU,2BAA2B;IACzC,OAAO,iBAAiB,CAAC,2BAA2B,EAAE,CAAC;AACzD,CAAC;AAED,MAAM,OAAO,SAAU,SAAQ,aAA6B;IAC1D,SAAS,GAAG,SAAS,EAAO,CAAC;IAE7B;;OAEG;IACH,KAAK,CAAC,eAAe;QACnB,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,eAAe,EAAE,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc;QAClB,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,cAAc,EAAE,CAAC;IACxD,CAAC;IAED;;;;;;;;;OASG;IACH,KAAK,CAAC,qBAAqB;QACzB,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,qBAAqB,EAAE,CAAC;IAC/D,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,oBAAoB;QACxB,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,oBAAoB,EAAE,CAAC;IAC9D,CAAC;IAED,MAAM;QACJ,MAAM,EAAE,MAAM,EAAE,GAAG,KAAK,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;QACxC,MAAM,QAAQ,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;QAErC,IAAI,sBAAsB,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,KAAK,aAAa,EAAE,CAAC;YACvE,OAAO,CAAC,sBAAsB,CAAC,IAAI,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAG,CAAC;QACtF,CAAC;QACD,OAAO,CAAC,eAAe,CAAC,IAAI,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAG,CAAC;IAC/E,CAAC;CACF;AAED,gFAAgF;AAChF,gEAAgE;AAChE,yEAAyE;AACzE,SAAS,WAAW,CAAC,MAA4B;IAC/C,IAAI,MAAM,YAAY,iBAAiB,CAAC,WAAW,EAAE,CAAC;QACpD,mBAAmB;QACnB,OAAO,MAAM,CAAC,yBAAyB,CAAC;IAC1C,CAAC;IACD,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;QAC/B,OAAO,MAAM,CAAC;IAChB,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC", "sourcesContent": ["import { ReactNode, PureComponent, createRef } from 'react';\n\nimport NativeVideoModule from './NativeVideoModule';\nimport NativeVideoView, { NativeTextureVideoView } from './NativeVideoView';\nimport type { VideoPlayer } from './VideoPlayer.types';\nimport type { VideoViewProps } from './VideoView.types';\n\n/**\n * Returns whether the current device supports Picture in Picture (PiP) mode.\n *\n * > **Note:** All major web browsers support Picture in Picture (PiP) mode except Firefox.\n * > For more information, see [MDN web docs](https://developer.mozilla.org/en-US/docs/Web/API/Picture-in-Picture_API#browser_compatibility).\n * @returns A `boolean` which is `true` if the device supports PiP mode, and `false` otherwise.\n * @platform android\n * @platform ios\n * @platform web\n */\nexport function isPictureInPictureSupported(): boolean {\n  return NativeVideoModule.isPictureInPictureSupported();\n}\n\nexport class VideoView extends PureComponent<VideoViewProps> {\n  nativeRef = createRef<any>();\n\n  /**\n   * Enters fullscreen mode.\n   */\n  async enterFullscreen(): Promise<void> {\n    return await this.nativeRef.current?.enterFullscreen();\n  }\n\n  /**\n   * Exits fullscreen mode.\n   */\n  async exitFullscreen(): Promise<void> {\n    return await this.nativeRef.current?.exitFullscreen();\n  }\n\n  /**\n   * Enters Picture in Picture (PiP) mode. Throws an exception if the device does not support PiP.\n   * > **Note:** Only one player can be in Picture in Picture (PiP) mode at a time.\n   *\n   * > **Note:** The `supportsPictureInPicture` property of the [config plugin](#configuration-in-app-config)\n   * > has to be configured for the PiP to work.\n   * @platform android\n   * @platform ios\n   * @platform web\n   */\n  async startPictureInPicture(): Promise<void> {\n    return await this.nativeRef.current?.startPictureInPicture();\n  }\n\n  /**\n   * Exits Picture in Picture (PiP) mode.\n   * @platform android\n   * @platform ios\n   * @platform web\n   */\n  async stopPictureInPicture(): Promise<void> {\n    return await this.nativeRef.current?.stopPictureInPicture();\n  }\n\n  render(): ReactNode {\n    const { player, ...props } = this.props;\n    const playerId = getPlayerId(player);\n\n    if (NativeTextureVideoView && this.props.surfaceType === 'textureView') {\n      return <NativeTextureVideoView {...props} player={playerId} ref={this.nativeRef} />;\n    }\n    return <NativeVideoView {...props} player={playerId} ref={this.nativeRef} />;\n  }\n}\n\n// Temporary solution to pass the shared object ID instead of the player object.\n// We can't really pass it as an object in the old architecture.\n// Technically we can in the new architecture, but it's not possible yet.\nfunction getPlayerId(player: number | VideoPlayer): number | null {\n  if (player instanceof NativeVideoModule.VideoPlayer) {\n    // @ts-expect-error\n    return player.__expo_shared_object_id__;\n  }\n  if (typeof player === 'number') {\n    return player;\n  }\n  return null;\n}\n"]}