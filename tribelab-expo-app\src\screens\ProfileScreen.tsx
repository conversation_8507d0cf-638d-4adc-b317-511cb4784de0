import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from "react-native";
import Icon from "react-native-vector-icons/Ionicons";
import ImageUpload from "../components/ImageUpload";
import { Avatar } from "../components/R2Image";
import { userAPI } from "../services/api";
import AsyncStorage from "@react-native-async-storage/async-storage";

interface UserProfile {
  id: string;
  name: string;
  username: string;
  email: string;
  bio?: string;
  profileImage?: string;
  points: number;
  level: number;
  followersCount: number;
  followingCount: number;
  communitiesCount: number;
}

const ProfileScreen = () => {
  const [user, setUser] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);

  useEffect(() => {
    loadUserProfile();
  }, []);

  const loadUserProfile = async () => {
    try {
      setLoading(true);
      const response = await userAPI.getProfile();

      if (response.data.success) {
        setUser(response.data.user);
      } else {
        Alert.alert("Error", "Failed to load profile");
      }
    } catch (error) {
      console.error("Profile load error:", error);
      Alert.alert("Error", "Failed to load profile");
    } finally {
      setLoading(false);
    }
  };

  const handleImageUpload = async (imageUrl: string, fileName: string) => {
    try {
      setUploading(true);

      // Update user profile with new image
      if (user) {
        setUser({ ...user, profileImage: imageUrl });
      }

      Alert.alert("Success", "Profile image updated successfully!");
    } catch (error) {
      console.error("Image upload error:", error);
      Alert.alert("Error", "Failed to update profile image");
    } finally {
      setUploading(false);
    }
  };

  const getLevel = (points: number) => {
    if (points < 100) return { level: 1, title: "Seedling I", nextLevel: 100 };
    if (points < 250) return { level: 2, title: "Seedling II", nextLevel: 250 };
    if (points < 500) return { level: 3, title: "Sprout I", nextLevel: 500 };
    if (points < 1000) return { level: 4, title: "Sprout II", nextLevel: 1000 };
    return { level: 5, title: "Tree", nextLevel: null };
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text>Loading profile...</Text>
      </View>
    );
  }

  if (!user) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text>Failed to load profile</Text>
        <TouchableOpacity onPress={loadUserProfile} style={styles.retryButton}>
          <Text style={styles.retryText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const levelInfo = getLevel(user.points);
  const pointsToNext = levelInfo.nextLevel
    ? levelInfo.nextLevel - user.points
    : 0;

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.headerButton}>
          <Icon name="menu" size={24} color="#000" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{user.name}</Text>
        <TouchableOpacity style={styles.headerButton}>
          <Icon name="search" size={24} color="#000" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.headerButton}>
          <Icon name="ellipsis-horizontal" size={24} color="#000" />
        </TouchableOpacity>
      </View>

      {/* Profile Info */}
      <View style={styles.profileContainer}>
        <View style={styles.profileImageContainer}>
          <ImageUpload
            currentImageUrl={user.profileImage}
            onImageUploaded={handleImageUpload}
            uploadType="profile"
            size={120}
            borderRadius={60}
            disabled={uploading}
            placeholder="Tap to upload profile image"
          />
          <View style={styles.levelBadge}>
            <Text style={styles.levelText}>{levelInfo.level}</Text>
          </View>
        </View>

        <Text style={styles.levelTitle}>
          Level {levelInfo.level} - {levelInfo.title}
        </Text>
        {pointsToNext > 0 ? (
          <Text style={styles.levelProgress}>
            {pointsToNext} points to level up
          </Text>
        ) : (
          <Text style={styles.levelProgress}>Max level reached!</Text>
        )}

        <Text style={styles.profileName}>{user.name}</Text>
        <Text style={styles.profileUsername}>@{user.username}</Text>
        {user.bio && <Text style={styles.profileBio}>{user.bio}</Text>}

        <TouchableOpacity style={styles.editProfileButton}>
          <Text style={styles.editProfileText}>EDIT PROFILE</Text>
        </TouchableOpacity>

        <View style={styles.statusContainer}>
          <View style={styles.statusItem}>
            <Text style={styles.statusValue}>Online now</Text>
          </View>
          <View style={styles.statusDivider} />
          <View style={styles.statusItem}>
            <Text style={styles.statusValue}>Joined Jun 2, 2025</Text>
          </View>
          <View style={styles.statusDivider} />
          <View style={styles.statusItem}>
            <Text style={styles.statusValue}>ISFJ</Text>
          </View>
        </View>

        <View style={styles.statsContainer}>
          <View style={styles.statsItem}>
            <Text style={styles.statsValue}>1</Text>
            <Text style={styles.statsLabel}>Contributions</Text>
          </View>
          <View style={styles.statsItem}>
            <Text style={styles.statsValue}>0</Text>
            <Text style={styles.statsLabel}>Followers</Text>
          </View>
          <View style={styles.statsItem}>
            <Text style={styles.statsValue}>6</Text>
            <Text style={styles.statsLabel}>Following</Text>
          </View>
        </View>
      </View>

      {/* Activity Section */}
      <View style={styles.sectionContainer}>
        <Text style={styles.sectionTitle}>Activity</Text>
        <View style={styles.activityEmptyState}>
          <Text style={styles.activityEmptyText}>
            No contributions to Day by Day Wellness Club yet.
          </Text>
        </View>
      </View>

      {/* Memberships Section */}
      <View style={styles.sectionContainer}>
        <Text style={styles.sectionTitle}>Memberships</Text>
        <View style={styles.membershipsList}>
          {/* Day by Day Wellness Club */}
          <TouchableOpacity style={styles.membershipItem}>
            <Image
              source={{ uri: "https://via.placeholder.com/50" }}
              style={styles.membershipImage}
            />
            <View style={styles.membershipInfo}>
              <Text style={styles.membershipName}>
                Day by Day Wellness Club
              </Text>
              <Text style={styles.membershipMeta}>54.8k members • Free</Text>
            </View>
          </TouchableOpacity>

          {/* Business AI Alliance */}
          <TouchableOpacity style={styles.membershipItem}>
            <Image
              source={{ uri: "https://via.placeholder.com/50" }}
              style={styles.membershipImage}
            />
            <View style={styles.membershipInfo}>
              <Text style={styles.membershipName}>Business AI Alliance</Text>
              <Text style={styles.membershipMeta}>5.6k members • Free</Text>
            </View>
          </TouchableOpacity>

          {/* More memberships would be listed here */}
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 15,
    paddingTop: 50,
    paddingBottom: 10,
    backgroundColor: "#fff",
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "bold",
    flex: 1,
    textAlign: "center",
  },
  headerButton: {
    padding: 5,
  },
  profileContainer: {
    backgroundColor: "#fff",
    alignItems: "center",
    padding: 20,
  },
  profileImageContainer: {
    position: "relative",
    marginBottom: 10,
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  levelBadge: {
    position: "absolute",
    bottom: 0,
    right: 0,
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: "#4080ff",
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: "#fff",
  },
  levelText: {
    color: "#fff",
    fontWeight: "bold",
    fontSize: 14,
  },
  levelTitle: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 5,
  },
  levelProgress: {
    fontSize: 14,
    color: "#666",
    marginBottom: 15,
  },
  profileName: {
    fontSize: 22,
    fontWeight: "bold",
    marginBottom: 5,
  },
  profileUsername: {
    fontSize: 16,
    color: "#666",
    marginBottom: 10,
  },
  profileBio: {
    fontSize: 16,
    marginBottom: 15,
  },
  editProfileButton: {
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 5,
    marginBottom: 15,
  },
  editProfileText: {
    fontSize: 16,
    fontWeight: "500",
  },
  statusContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 20,
  },
  statusItem: {
    paddingHorizontal: 10,
  },
  statusValue: {
    fontSize: 14,
    color: "#666",
  },
  statusDivider: {
    width: 1,
    height: 20,
    backgroundColor: "#ddd",
  },
  statsContainer: {
    flexDirection: "row",
    width: "100%",
    borderTopWidth: 1,
    borderTopColor: "#eee",
    paddingTop: 15,
  },
  statsItem: {
    flex: 1,
    alignItems: "center",
  },
  statsValue: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 5,
  },
  statsLabel: {
    fontSize: 14,
    color: "#666",
  },
  sectionContainer: {
    backgroundColor: "#fff",
    marginTop: 10,
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 15,
  },
  activityEmptyState: {
    padding: 20,
    alignItems: "center",
  },
  activityEmptyText: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
  },
  membershipsList: {
    marginTop: 10,
  },
  membershipItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  membershipImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 15,
  },
  membershipInfo: {
    flex: 1,
  },
  membershipName: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 5,
  },
  membershipMeta: {
    fontSize: 14,
    color: "#666",
  },
  centered: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  retryButton: {
    marginTop: 20,
    paddingVertical: 10,
    paddingHorizontal: 20,
    backgroundColor: "#007AFF",
    borderRadius: 5,
  },
  retryText: {
    color: "white",
    fontWeight: "bold",
  },
});

export default ProfileScreen;
