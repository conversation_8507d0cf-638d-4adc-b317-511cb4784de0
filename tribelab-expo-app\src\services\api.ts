import axios, { Axios<PERSON>rror, AxiosRequestConfig, AxiosResponse } from "axios";
import AsyncStorage from "@react-native-async-storage/async-storage";
import NetInfo from "@react-native-community/netinfo";
import Toast from "react-native-toast-message";

// Types for better error handling
interface ApiError {
  message: string;
  status?: number;
  code?: string;
  details?: any;
}

interface CacheEntry {
  data: any;
  timestamp: number;
  expiry: number;
}

// Cache configuration
const CACHE_DURATION = {
  SHORT: 5 * 60 * 1000, // 5 minutes
  MEDIUM: 30 * 60 * 1000, // 30 minutes
  LONG: 24 * 60 * 60 * 1000, // 24 hours
};

// In-memory cache
const cache = new Map<string, CacheEntry>();

// Offline queue for failed requests
const offlineQueue: Array<{
  config: AxiosRequestConfig;
  resolve: (value: any) => void;
  reject: (reason: any) => void;
}> = [];

// Development mode configuration
const IS_DEVELOPMENT = __DEV__;
const MOCK_API_ENABLED = process.env.ENABLE_MOCK_API === "true" || false;

// Get API base URL from environment or use defaults
const getApiBaseUrl = () => {
  if (IS_DEVELOPMENT && MOCK_API_ENABLED) {
    return "http://localhost:3000"; // Mock server (will be intercepted)
  }

  if (IS_DEVELOPMENT) {
    return process.env.API_BASE_URL_DEV || "http://localhost:3000";
  }

  return process.env.API_BASE_URL_PROD || "https://thetribelab.com";
};

// Create axios instance with default config
const api = axios.create({
  baseURL: getApiBaseUrl(),
  timeout: 15000,
  headers: {
    "Content-Type": "application/json",
  },
});

// Log the API configuration for debugging
console.log("🔧 API Configuration:");
console.log("- Base URL:", getApiBaseUrl());
console.log("- Development Mode:", IS_DEVELOPMENT);
console.log("- Mock API Enabled:", MOCK_API_ENABLED);

// Utility functions
const getCacheKey = (config: AxiosRequestConfig): string => {
  return `${config.method}_${config.url}_${JSON.stringify(config.params || {})}`;
};

const isOnline = async (): Promise<boolean> => {
  const netInfo = await NetInfo.fetch();
  return netInfo.isConnected === true;
};

const getCachedData = (key: string): any | null => {
  const entry = cache.get(key);
  if (entry && Date.now() < entry.timestamp + entry.expiry) {
    return entry.data;
  }
  if (entry) {
    cache.delete(key); // Remove expired entry
  }
  return null;
};

const setCachedData = (
  key: string,
  data: any,
  expiry: number = CACHE_DURATION.MEDIUM
): void => {
  cache.set(key, {
    data,
    timestamp: Date.now(),
    expiry,
  });
};

const showErrorToast = (message: string): void => {
  Toast.show({
    type: "error",
    text1: "Error",
    text2: message,
    visibilityTime: 4000,
  });
};

const showOfflineToast = (): void => {
  Toast.show({
    type: "info",
    text1: "Offline",
    text2: "Request queued for when connection is restored",
    visibilityTime: 3000,
  });
};

// Mock API responses for development
const mockApiResponses = {
  "POST_/auth/login": {
    data: {
      token: "mock_jwt_token_12345",
      refreshToken: "mock_refresh_token_67890",
      user: {
        id: "1",
        email: "<EMAIL>",
        name: "Test User",
        role: "user",
        avatar: null,
        isEmailVerified: true,
      },
    },
    status: 200,
  },
  "POST_/auth/register": {
    data: {
      message: "Registration successful! Please verify your email.",
      user: {
        id: "2",
        email: "<EMAIL>",
        name: "New User",
        isEmailVerified: false,
      },
    },
    status: 201,
  },
};

// Request interceptor for adding auth token and caching
api.interceptors.request.use(
  async (config) => {
    // Mock API responses in development
    if (IS_DEVELOPMENT && MOCK_API_ENABLED) {
      const mockKey = `${config.method?.toUpperCase()}_${config.url}`;
      const mockResponse =
        mockApiResponses[mockKey as keyof typeof mockApiResponses];

      if (mockResponse) {
        console.log(`🔄 MOCK API: ${mockKey} - Returning mock data`);

        // Simulate network delay
        await new Promise((resolve) => setTimeout(resolve, 800));

        // Return mock response as a successful response
        return Promise.reject({
          config,
          response: {
            data: mockResponse.data,
            status: mockResponse.status,
            statusText: "OK (Mock)",
            headers: {},
            config,
          },
          isAxiosError: false,
          mock: true,
        });
      }
    }

    // Add auth token
    const token = await AsyncStorage.getItem("auth_token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Check cache for GET requests
    if (config.method === "get") {
      const cacheKey = getCacheKey(config);
      const cachedData = getCachedData(cacheKey);
      if (cachedData) {
        // Return cached data as a resolved promise
        return Promise.reject({
          config,
          response: { data: cachedData },
          cached: true,
        });
      }
    }

    // Check network connectivity
    const online = await isOnline();
    if (!online && config.method !== "get") {
      // Queue non-GET requests for later
      return Promise.reject({
        config,
        offline: true,
        message: "No internet connection",
      });
    }

    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for error handling and caching
api.interceptors.response.use(
  (response: AxiosResponse) => {
    // Cache successful GET responses
    if (response.config.method === "get") {
      const cacheKey = getCacheKey(response.config);
      const expiry = response.config.url?.includes("/notifications")
        ? CACHE_DURATION.SHORT
        : response.config.url?.includes("/analytics")
          ? CACHE_DURATION.MEDIUM
          : CACHE_DURATION.LONG;
      setCachedData(cacheKey, response.data, expiry);
    }

    return response;
  },
  async (error) => {
    // Handle mock responses
    if (error.mock) {
      console.log(`✅ MOCK API: Successful mock response`);
      return Promise.resolve(error.response);
    }

    // Handle cached responses
    if (error.cached) {
      return Promise.resolve(error.response);
    }

    // Handle offline requests
    if (error.offline) {
      return new Promise((resolve, reject) => {
        offlineQueue.push({
          config: error.config,
          resolve,
          reject,
        });
        showOfflineToast();
      });
    }

    const originalRequest = error.config;

    // Handle token refresh if 401 error
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = await AsyncStorage.getItem("refresh_token");
        if (!refreshToken) {
          throw new Error("No refresh token available");
        }

        const response = await axios.post(
          "https://api.tribelab.com/auth/refresh-token",
          { refreshToken }
        );

        const { token } = response.data;
        await AsyncStorage.setItem("auth_token", token);

        originalRequest.headers.Authorization = `Bearer ${token}`;
        return api(originalRequest);
      } catch (refreshError) {
        // Handle refresh token failure (logout user, etc.)
        await AsyncStorage.removeItem("auth_token");
        await AsyncStorage.removeItem("refresh_token");

        // Redirect to login or show auth error
        showErrorToast("Session expired. Please login again.");

        return Promise.reject(refreshError);
      }
    }

    // Handle different error types
    const apiError: ApiError = {
      message: "An unexpected error occurred",
      status: error.response?.status,
      code: error.code,
      details: error.response?.data,
    };

    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response;

      switch (status) {
        case 400:
          apiError.message = data?.message || "Bad request";
          break;
        case 403:
          apiError.message = data?.message || "Access forbidden";
          break;
        case 404:
          apiError.message = data?.message || "Resource not found";
          break;
        case 422:
          apiError.message = data?.message || "Validation error";
          break;
        case 429:
          apiError.message = "Too many requests. Please try again later.";
          break;
        case 500:
          apiError.message = "Server error. Please try again later.";
          break;
        default:
          apiError.message = data?.message || `Error ${status}`;
      }
    } else if (error.request) {
      // Network error
      apiError.message = "Network error. Please check your connection.";
    } else {
      // Other error
      apiError.message = error.message || "An unexpected error occurred";
    }

    // Show error toast for user-facing errors
    if (error.response?.status !== 401) {
      showErrorToast(apiError.message);
    }

    return Promise.reject(apiError);
  }
);

// Offline queue processing
const processOfflineQueue = async (): Promise<void> => {
  const online = await isOnline();
  if (!online || offlineQueue.length === 0) {
    return;
  }

  console.log(`Processing ${offlineQueue.length} queued requests`);

  const queueCopy = [...offlineQueue];
  offlineQueue.length = 0; // Clear the queue

  for (const { config, resolve, reject } of queueCopy) {
    try {
      const response = await api(config);
      resolve(response);
    } catch (error) {
      reject(error);
    }
  }
};

// Listen for network changes
NetInfo.addEventListener((state) => {
  if (state.isConnected) {
    processOfflineQueue();
  }
});

// Cache management functions
export const clearCache = (): void => {
  cache.clear();
};

export const clearExpiredCache = (): void => {
  const now = Date.now();
  for (const [key, entry] of cache.entries()) {
    if (now >= entry.timestamp + entry.expiry) {
      cache.delete(key);
    }
  }
};

export const getCacheSize = (): number => {
  return cache.size;
};

// Retry mechanism for failed requests
export const retryRequest = async (
  config: AxiosRequestConfig,
  maxRetries: number = 3
): Promise<any> => {
  let lastError;

  for (let i = 0; i <= maxRetries; i++) {
    try {
      const response = await api(config);
      return response;
    } catch (error) {
      lastError = error;

      if (i < maxRetries) {
        // Wait before retrying (exponential backoff)
        const delay = Math.pow(2, i) * 1000;
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
  }

  throw lastError;
};

// API endpoints
export const authAPI = {
  login: (email: string, password: string) =>
    api.post("/api/auth/signin", { email, password }),

  register: (userData: any) => api.post("/api/auth/register", userData),

  verifyEmail: (token: string) => api.post("/api/auth/verify-email", { token }),

  resendVerification: (email: string) =>
    api.post("/api/auth/resend-verification", { email }),
};

export const userAPI = {
  getProfile: () => api.get("/api/user/profile"),

  updateProfile: (userData: any) => api.put("/api/user/settings", userData),

  uploadProfileImage: (formData: FormData) =>
    api.post("/api/user/profile-image", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }),

  getCommunities: () => api.get("/api/user/communities"),
};

export const communityAPI = {
  getCommunities: (page: number = 1, limit: number = 20) =>
    api.get(`/api/community?page=${page}&limit=${limit}`),

  getCommunityById: (id: string) => api.get(`/api/community/${id}`),

  getCommunityBySlug: (slug: string) => api.get(`/api/community?slug=${slug}`),

  createCommunity: (communityData: any) =>
    api.post("/api/community", communityData),

  updateCommunity: (communityId: string, communityData: any) =>
    api.put(`/api/community/${communityId}`, communityData),

  deleteCommunity: (communityId: string) =>
    api.delete(`/api/community/${communityId}`),

  joinCommunity: (communityId: string, message?: string) =>
    api.post(`/api/community/join`, { communityId, message }),

  leaveCommunity: (communityId: string) =>
    api.post(`/api/community/leave`, { communityId }),

  getCommunityPosts: (communityId: string, page: number = 1) =>
    api.get(`/api/community/${communityId}/posts?page=${page}`),

  createPost: (communityId: string, postData: any) =>
    api.post(`/api/community/${communityId}/posts`, postData),

  likePost: (postId: string) => api.post(`/api/posts/${postId}/like`),

  getCommunityMembers: (communityId: string, page: number = 1) =>
    api.get(`/api/community/${communityId}/members?page=${page}`),

  getCommunityEvents: (communityId: string) =>
    api.get(`/api/community/${communityId}/events`),

  createEvent: (communityId: string, eventData: any) =>
    api.post(`/api/community/${communityId}/events`, eventData),

  getJoinRequests: (communityId: string) =>
    api.get(`/api/community/requests?communityId=${communityId}`),

  approveJoinRequest: (communityId: string, userId: string) =>
    api.post(`/api/community/requests/${userId}/approve`, { communityId }),

  rejectJoinRequest: (communityId: string, userId: string) =>
    api.post(`/api/community/requests/${userId}/reject`, { communityId }),

  getLeaderboard: (communityId: string) =>
    api.get(`/api/community/${communityId}/leaderboard`),
};

export const messageAPI = {
  getConversations: () => api.get("/messages"),

  getMessages: (userId: string) => api.get(`/messages/${userId}`),

  sendMessage: (userId: string, content: string) =>
    api.post(`/messages/${userId}`, { content }),
};

// Course API
export const courseAPI = {
  getCourses: (page: number = 1, filters?: any) =>
    api.get(
      `/api/courses?page=${page}${filters ? `&${new URLSearchParams(filters).toString()}` : ""}`
    ),

  getCourseById: (courseId: string) => api.get(`/api/courses/${courseId}`),

  createCourse: (courseData: any) => api.post("/api/courses", courseData),

  updateCourse: (courseId: string, courseData: any) =>
    api.put(`/api/courses/${courseId}`, courseData),

  deleteCourse: (courseId: string) => api.delete(`/api/courses/${courseId}`),

  enrollInCourse: (courseId: string) =>
    api.post(`/api/courses/${courseId}/enroll`),

  getEnrolledCourses: (page: number = 1) =>
    api.get(`/api/courses/enrolled?page=${page}`),

  updateLessonProgress: (lessonId: string, progress: any) =>
    api.put(`/api/lessons/${lessonId}/progress`, progress),

  createModule: (courseId: string, moduleData: any) =>
    api.post(`/api/courses/${courseId}/modules`, moduleData),

  updateModule: (moduleId: string, moduleData: any) =>
    api.put(`/api/modules/${moduleId}`, moduleData),

  deleteModule: (moduleId: string) => api.delete(`/api/modules/${moduleId}`),

  createLesson: (moduleId: string, lessonData: any) =>
    api.post(`/api/modules/${moduleId}/lessons`, lessonData),

  updateLesson: (lessonId: string, lessonData: any) =>
    api.put(`/api/lessons/${lessonId}`, lessonData),

  deleteLesson: (lessonId: string) => api.delete(`/api/lessons/${lessonId}`),

  getCourseProgress: (courseId: string) =>
    api.get(`/api/courses/${courseId}/progress`),

  markLessonComplete: (lessonId: string) =>
    api.post(`/api/lessons/${lessonId}/complete`),
};

export const paymentAPI = {
  getPaymentMethods: () => api.get("/payments/methods"),

  addPaymentMethod: (paymentData: any) =>
    api.post("/payments/methods", paymentData),

  removePaymentMethod: (paymentMethodId: string) =>
    api.delete(`/payments/methods/${paymentMethodId}`),

  getSubscriptions: () => api.get("/payments/subscriptions"),

  subscribe: (planId: string, paymentMethodId?: string) =>
    api.post("/payments/subscribe", { planId, paymentMethodId }),

  cancelSubscription: (subscriptionId: string, immediate?: boolean) =>
    api.post("/payments/cancel", { subscriptionId, immediate }),

  getTransactions: (page: number = 1) =>
    api.get(`/payments/transactions?page=${page}`),

  getPlans: (communityId?: string) =>
    api.get(
      `/payments/plans${communityId ? `?communityId=${communityId}` : ""}`
    ),

  processPayment: (paymentData: any) =>
    api.post("/payments/process", paymentData),
};

// Enhanced Notification API (removing duplicate)
const enhancedNotificationAPI = {
  getNotifications: (page: number = 1, filters?: any) =>
    api.get(
      `/notifications?page=${page}${filters ? `&${new URLSearchParams(filters).toString()}` : ""}`
    ),

  markAsRead: (notificationId: string) =>
    api.put(`/notifications/${notificationId}/read`),

  markAllAsRead: () => api.put("/notifications/mark-all-read"),

  deleteNotification: (notificationId: string) =>
    api.delete(`/notifications/${notificationId}`),

  getSettings: () => api.get("/notifications/settings"),

  updateSettings: (settings: any) =>
    api.put("/notifications/settings", settings),
};

// Export the enhanced version
export { enhancedNotificationAPI as notificationAPI };

// Chat API
export const chatAPI = {
  getChats: (page: number = 1) => api.get(`/messages?page=${page}`),

  getMessages: (chatId: string, page: number = 1) =>
    api.get(`/messages/${chatId}?page=${page}`),

  sendMessage: (chatId: string, messageData: any) =>
    api.post(`/messages/${chatId}`, messageData),

  createChat: (chatData: any) => api.post("/messages", chatData),

  markAsRead: (chatId: string) => api.put(`/messages/${chatId}/read`),

  deleteMessage: (messageId: string) =>
    api.delete(`/messages/message/${messageId}`),

  updateChatSettings: (chatId: string, settings: any) =>
    api.put(`/messages/${chatId}/settings`, settings),
};

// Search API
export const searchAPI = {
  search: (query: string, filters?: any, page: number = 1) =>
    api.get(
      `/search?q=${encodeURIComponent(query)}&page=${page}${filters ? `&${new URLSearchParams(filters).toString()}` : ""}`
    ),

  getSuggestions: (query: string) =>
    api.get(`/search/suggestions?q=${encodeURIComponent(query)}`),

  getTrendingSearches: () => api.get("/search/trending"),
};

// Admin API
export const adminAPI = {
  getAnalytics: (period: string = "30d") =>
    api.get(`/admin/analytics?period=${period}`),

  getUsers: (page: number = 1, filters?: any) =>
    api.get(
      `/admin/users?page=${page}${filters ? `&${new URLSearchParams(filters).toString()}` : ""}`
    ),

  getCommunities: (page: number = 1, filters?: any) =>
    api.get(
      `/admin/communities?page=${page}${filters ? `&${new URLSearchParams(filters).toString()}` : ""}`
    ),

  updateUserStatus: (userId: string, status: string, reason?: string) =>
    api.put(`/admin/users/${userId}/status`, { status, reason }),

  updateCommunityStatus: (
    communityId: string,
    status: string,
    reason?: string
  ) => api.put(`/admin/communities/${communityId}/status`, { status, reason }),

  getReports: (page: number = 1, filters?: any) =>
    api.get(
      `/admin/reports?page=${page}${filters ? `&${new URLSearchParams(filters).toString()}` : ""}`
    ),

  reviewReport: (reportId: string, status: string, action?: string) =>
    api.put(`/admin/reports/${reportId}`, { status, action }),
};

export default api;
