export {
  isPictureInPictureSupported,
  clearVideoCacheAsync,
  setVideoCacheSizeAsync,
  getCurrentVideoCacheSize,
} from './VideoModule';
export { VideoView } from './VideoView';
export { useVideoPlayer } from './VideoPlayer';

export { VideoContentFit, VideoViewProps, SurfaceType } from './VideoView.types';
export { VideoThumbnail } from './VideoThumbnail';

export { createVideoPlayer } from './VideoPlayer';

export {
  VideoPlayer,
  VideoPlayerStatus,
  VideoSource,
  PlayerError,
  VideoMetadata,
  DRMType,
  DRMOptions,
  BufferOptions,
  AudioMixingMode,
  VideoThumbnailOptions,
  VideoSize,
  SubtitleTrack,
  AudioTrack,
  VideoTrack,
  ContentType,
} from './VideoPlayer.types';

export {
  VideoPlayerEvents,
  StatusChangeEventPayload,
  PlayingChangeEventPayload,
  PlaybackRateChangeEventPayload,
  VolumeChangeEventPayload,
  MutedChangeEventPayload,
  TimeUpdateEventPayload,
  SourceChangeEventPayload,
  SourceLoadEventPayload,
} from './VideoPlayerEvents.types';
