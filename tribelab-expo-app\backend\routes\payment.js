import express from 'express';
import crypto from 'crypto';
import { protect } from '../middleware/auth.js';

const router = express.Router();

// Razorpay configuration
const RAZORPAY_KEY_ID = process.env.RAZORPAY_KEY_ID;
const RAZORPAY_KEY_SECRET = process.env.RAZORPAY_KEY_SECRET;

// Mock Razorpay client (in production, use actual Razorpay SDK)
const createRazorpayOrder = async (orderData) => {
  // This is a mock implementation
  // In production, you would use: const Razorpay = require('razorpay');
  return {
    id: `order_${Date.now()}${Math.random().toString(36).substr(2, 9)}`,
    amount: orderData.amount,
    currency: orderData.currency || 'INR',
    status: 'created',
    created_at: Math.floor(Date.now() / 1000),
  };
};

// @desc    Create Razorpay order
// @route   POST /api/payments/create-order
// @access  Private
router.post('/create-order', protect, async (req, res, next) => {
  try {
    const { amount, currency = 'INR', description, notes = {} } = req.body;

    // Validate amount
    if (!amount || amount < 100) {
      return res.status(400).json({
        success: false,
        error: 'Invalid amount. Minimum amount is ₹1 (100 paise)'
      });
    }

    if (amount > ********) {
      return res.status(400).json({
        success: false,
        error: 'Amount exceeds maximum limit of ₹5,00,000'
      });
    }

    // Create order with Razorpay
    const orderData = {
      amount: amount, // amount in paise
      currency: currency,
      receipt: `receipt_${req.user.id}_${Date.now()}`,
      notes: {
        userId: req.user.id,
        description: description,
        ...notes
      }
    };

    const order = await createRazorpayOrder(orderData);

    // In production, you might want to save order details to database
    // await saveOrderToDatabase(order, req.user.id);

    res.status(200).json({
      success: true,
      orderId: order.id,
      amount: order.amount,
      currency: order.currency,
      keyId: RAZORPAY_KEY_ID
    });

  } catch (error) {
    console.error('Create order error:', error);
    next(error);
  }
});

// @desc    Verify payment signature
// @route   POST /api/payments/verify
// @access  Private
router.post('/verify', protect, async (req, res, next) => {
  try {
    const { paymentId, orderId, signature } = req.body;

    if (!paymentId || !orderId || !signature) {
      return res.status(400).json({
        success: false,
        error: 'Missing payment verification data'
      });
    }

    // Verify signature
    const body = orderId + '|' + paymentId;
    const expectedSignature = crypto
      .createHmac('sha256', RAZORPAY_KEY_SECRET)
      .update(body.toString())
      .digest('hex');

    const isAuthentic = expectedSignature === signature;

    if (!isAuthentic) {
      return res.status(400).json({
        success: false,
        error: 'Invalid payment signature'
      });
    }

    // Payment is verified
    // In production, you would:
    // 1. Update order status in database
    // 2. Grant access to purchased content
    // 3. Send confirmation email
    // 4. Update user subscription status

    console.log(`✅ Payment verified: ${paymentId} for user ${req.user.id}`);

    res.status(200).json({
      success: true,
      message: 'Payment verified successfully',
      paymentId,
      orderId
    });

  } catch (error) {
    console.error('Payment verification error:', error);
    next(error);
  }
});

// @desc    Get payment history
// @route   GET /api/payments/history
// @access  Private
router.get('/history', protect, async (req, res, next) => {
  try {
    const { page = 1, limit = 20 } = req.query;

    // In production, fetch from database
    // For now, return mock data
    const mockPayments = [
      {
        id: 'pay_123456789',
        orderId: 'order_987654321',
        amount: 99900, // ₹999 in paise
        currency: 'INR',
        status: 'captured',
        description: 'Monthly subscription to Tech Community',
        createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
        method: 'card'
      },
      {
        id: 'pay_987654321',
        orderId: 'order_123456789',
        amount: 199900, // ₹1999 in paise
        currency: 'INR',
        status: 'captured',
        description: 'Purchase: Advanced React Course',
        createdAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000), // 14 days ago
        method: 'upi'
      }
    ];

    const total = mockPayments.length;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedPayments = mockPayments.slice(startIndex, endIndex);

    res.status(200).json({
      success: true,
      payments: paginatedPayments,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        hasNext: endIndex < total,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error('Payment history error:', error);
    next(error);
  }
});

// @desc    Get community pricing
// @route   GET /api/payments/community/:communityId/pricing
// @access  Public
router.get('/community/:communityId/pricing', async (req, res, next) => {
  try {
    const { communityId } = req.params;

    // In production, fetch from database
    // For now, return mock pricing
    const mockPricing = {
      communityId,
      communityName: 'Tech Community',
      monthlyPrice: 999, // ₹999
      yearlyPrice: 9999, // ₹9999 (save ₹1989)
      currency: 'INR',
      features: [
        'Access to exclusive content',
        'Direct messaging with experts',
        'Weekly live sessions',
        'Course discounts',
        'Priority support'
      ],
      trialDays: 7
    };

    res.status(200).json({
      success: true,
      ...mockPricing
    });

  } catch (error) {
    console.error('Get pricing error:', error);
    next(error);
  }
});

// @desc    Process refund
// @route   POST /api/payments/:paymentId/refund
// @access  Private
router.post('/:paymentId/refund', protect, async (req, res, next) => {
  try {
    const { paymentId } = req.params;
    const { amount, reason } = req.body;

    // In production, you would:
    // 1. Verify user has permission to refund
    // 2. Check refund policy
    // 3. Process refund with Razorpay
    // 4. Update database

    // Mock refund response
    const refund = {
      id: `rfnd_${Date.now()}`,
      paymentId,
      amount: amount || 'full',
      status: 'processed',
      reason: reason || 'requested_by_customer',
      createdAt: new Date()
    };

    console.log(`💰 Refund processed: ${refund.id} for payment ${paymentId}`);

    res.status(200).json({
      success: true,
      message: 'Refund processed successfully',
      refund
    });

  } catch (error) {
    console.error('Refund error:', error);
    next(error);
  }
});

// @desc    Get payment configuration
// @route   GET /api/payments/config
// @access  Private
router.get('/config', protect, async (req, res) => {
  try {
    res.status(200).json({
      success: true,
      config: {
        keyId: RAZORPAY_KEY_ID,
        currency: 'INR',
        minAmount: 100, // ₹1 in paise
        maxAmount: ********, // ₹5 lakh in paise
        supportedMethods: ['card', 'netbanking', 'wallet', 'upi'],
        supportedCurrencies: ['INR']
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to get payment configuration'
    });
  }
});

// @desc    Webhook handler for Razorpay events
// @route   POST /api/payments/webhook
// @access  Public (but verified with webhook signature)
router.post('/webhook', express.raw({ type: 'application/json' }), async (req, res) => {
  try {
    const signature = req.headers['x-razorpay-signature'];
    const body = req.body;

    // Verify webhook signature
    const expectedSignature = crypto
      .createHmac('sha256', process.env.RAZORPAY_WEBHOOK_SECRET || '')
      .update(body)
      .digest('hex');

    if (signature !== expectedSignature) {
      return res.status(400).json({ error: 'Invalid signature' });
    }

    const event = JSON.parse(body.toString());

    // Handle different webhook events
    switch (event.event) {
      case 'payment.captured':
        console.log('Payment captured:', event.payload.payment.entity);
        // Update database, send confirmation email, etc.
        break;
      
      case 'payment.failed':
        console.log('Payment failed:', event.payload.payment.entity);
        // Handle failed payment
        break;
      
      case 'order.paid':
        console.log('Order paid:', event.payload.order.entity);
        // Handle successful order
        break;
      
      default:
        console.log('Unhandled webhook event:', event.event);
    }

    res.status(200).json({ status: 'ok' });

  } catch (error) {
    console.error('Webhook error:', error);
    res.status(500).json({ error: 'Webhook processing failed' });
  }
});

export default router;
