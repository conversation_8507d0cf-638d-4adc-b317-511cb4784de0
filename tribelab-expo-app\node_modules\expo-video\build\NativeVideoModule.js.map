{"version": 3, "file": "NativeVideoModule.js", "sourceRoot": "", "sources": ["../src/NativeVideoModule.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAexD,eAAe,mBAAmB,CAAkB,WAAW,CAAC,CAAC", "sourcesContent": ["import { requireNativeModule } from 'expo-modules-core';\n\nimport type { VideoPlayer } from './VideoPlayer.types';\nimport type { VideoThumbnail } from './VideoThumbnail';\n\ntype ExpoVideoModule = {\n  VideoPlayer: typeof VideoPlayer;\n  VideoThumbnail: typeof VideoThumbnail;\n\n  isPictureInPictureSupported(): boolean;\n  setVideoCacheSizeAsync(sizeBytes: number): Promise<void>;\n  clearVideoCacheAsync(): Promise<void>;\n  getCurrentVideoCacheSize(): number;\n};\n\nexport default requireNativeModule<ExpoVideoModule>('ExpoVideo');\n"]}