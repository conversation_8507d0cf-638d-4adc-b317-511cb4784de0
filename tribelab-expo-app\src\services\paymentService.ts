import RazorpayCheckout from 'react-native-razorpay';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface PaymentOptions {
  amount: number; // in paise (1 INR = 100 paise)
  currency?: string;
  orderId?: string;
  description: string;
  prefill?: {
    email?: string;
    contact?: string;
    name?: string;
  };
  notes?: Record<string, string>;
  theme?: {
    color?: string;
  };
}

interface PaymentResult {
  success: boolean;
  paymentId?: string;
  orderId?: string;
  signature?: string;
  error?: string;
  errorCode?: string;
  errorDescription?: string;
}

interface CreateOrderRequest {
  amount: number;
  currency?: string;
  description: string;
  notes?: Record<string, string>;
}

interface CreateOrderResponse {
  success: boolean;
  orderId?: string;
  amount?: number;
  currency?: string;
  error?: string;
}

class PaymentService {
  private razorpayKeyId: string;
  private apiBaseUrl: string;

  constructor() {
    this.razorpayKeyId = process.env.EXPO_PUBLIC_RAZORPAY_KEY_ID || '';
    this.apiBaseUrl = process.env.API_BASE_URL_DEV || 'http://localhost:4000';
    
    if (!this.razorpayKeyId) {
      console.warn('⚠️ Razorpay Key ID not configured');
    }
  }

  /**
   * Create order on backend
   */
  async createOrder(orderData: CreateOrderRequest): Promise<CreateOrderResponse> {
    try {
      const token = await this.getAuthToken();
      
      const response = await fetch(`${this.apiBaseUrl}/api/payments/create-order`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(orderData),
      });

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to create order');
      }

      return result;
    } catch (error) {
      console.error('Create order error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create order',
      };
    }
  }

  /**
   * Process payment using Razorpay
   */
  async processPayment(options: PaymentOptions): Promise<PaymentResult> {
    try {
      if (!this.razorpayKeyId) {
        throw new Error('Razorpay not configured');
      }

      // Create order first if orderId not provided
      let orderId = options.orderId;
      if (!orderId) {
        const orderResult = await this.createOrder({
          amount: options.amount,
          currency: options.currency || 'INR',
          description: options.description,
          notes: options.notes,
        });

        if (!orderResult.success || !orderResult.orderId) {
          throw new Error(orderResult.error || 'Failed to create order');
        }

        orderId = orderResult.orderId;
      }

      // Prepare Razorpay options
      const razorpayOptions = {
        description: options.description,
        image: 'https://pub-895f71ea78c843b59c97073ccfe523c5.r2.dev/logo.png', // Your app logo
        currency: options.currency || 'INR',
        key: this.razorpayKeyId,
        amount: options.amount,
        order_id: orderId,
        name: 'TribeLab',
        prefill: {
          email: options.prefill?.email || '',
          contact: options.prefill?.contact || '',
          name: options.prefill?.name || '',
        },
        theme: {
          color: options.theme?.color || '#007AFF',
        },
        notes: options.notes || {},
      };

      // Open Razorpay checkout
      const paymentData = await RazorpayCheckout.open(razorpayOptions);

      // Verify payment on backend
      const verificationResult = await this.verifyPayment({
        paymentId: paymentData.razorpay_payment_id,
        orderId: paymentData.razorpay_order_id,
        signature: paymentData.razorpay_signature,
      });

      if (verificationResult.success) {
        return {
          success: true,
          paymentId: paymentData.razorpay_payment_id,
          orderId: paymentData.razorpay_order_id,
          signature: paymentData.razorpay_signature,
        };
      } else {
        throw new Error('Payment verification failed');
      }

    } catch (error: any) {
      console.error('Payment error:', error);
      
      return {
        success: false,
        error: error.description || error.message || 'Payment failed',
        errorCode: error.code,
        errorDescription: error.description,
      };
    }
  }

  /**
   * Verify payment on backend
   */
  async verifyPayment(paymentData: {
    paymentId: string;
    orderId: string;
    signature: string;
  }): Promise<{ success: boolean; error?: string }> {
    try {
      const token = await this.getAuthToken();
      
      const response = await fetch(`${this.apiBaseUrl}/api/payments/verify`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(paymentData),
      });

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || 'Payment verification failed');
      }

      return result;
    } catch (error) {
      console.error('Payment verification error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Payment verification failed',
      };
    }
  }

  /**
   * Get payment history
   */
  async getPaymentHistory(page: number = 1, limit: number = 20): Promise<{
    success: boolean;
    payments?: any[];
    pagination?: any;
    error?: string;
  }> {
    try {
      const token = await this.getAuthToken();
      
      const response = await fetch(
        `${this.apiBaseUrl}/api/payments/history?page=${page}&limit=${limit}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        }
      );

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch payment history');
      }

      return result;
    } catch (error) {
      console.error('Payment history error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch payment history',
      };
    }
  }

  /**
   * Process community subscription payment
   */
  async subscribeToCommunity(communityId: string, planType: 'monthly' | 'yearly' = 'monthly'): Promise<PaymentResult> {
    try {
      // Get community pricing
      const pricingResponse = await fetch(`${this.apiBaseUrl}/api/community/${communityId}/pricing`);
      const pricingData = await pricingResponse.json();
      
      if (!pricingResponse.ok) {
        throw new Error(pricingData.error || 'Failed to get community pricing');
      }

      const amount = planType === 'yearly' 
        ? pricingData.yearlyPrice * 100 // Convert to paise
        : pricingData.monthlyPrice * 100;

      return await this.processPayment({
        amount,
        description: `${planType} subscription to ${pricingData.communityName}`,
        notes: {
          communityId,
          planType,
          subscriptionType: 'community',
        },
      });
    } catch (error) {
      console.error('Community subscription error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Subscription failed',
      };
    }
  }

  /**
   * Process course purchase payment
   */
  async purchaseCourse(courseId: string): Promise<PaymentResult> {
    try {
      // Get course pricing
      const courseResponse = await fetch(`${this.apiBaseUrl}/api/courses/${courseId}`);
      const courseData = await courseResponse.json();
      
      if (!courseResponse.ok) {
        throw new Error(courseData.error || 'Failed to get course details');
      }

      const amount = courseData.course.price * 100; // Convert to paise

      return await this.processPayment({
        amount,
        description: `Purchase: ${courseData.course.title}`,
        notes: {
          courseId,
          purchaseType: 'course',
        },
      });
    } catch (error) {
      console.error('Course purchase error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Course purchase failed',
      };
    }
  }

  /**
   * Get auth token from storage
   */
  private async getAuthToken(): Promise<string> {
    try {
      const token = await AsyncStorage.getItem('auth_token');
      return token || '';
    } catch (error) {
      console.error('Error getting auth token:', error);
      return '';
    }
  }

  /**
   * Format amount for display
   */
  formatAmount(amountInPaise: number, currency: string = 'INR'): string {
    const amount = amountInPaise / 100;
    
    if (currency === 'INR') {
      return `₹${amount.toFixed(2)}`;
    }
    
    return `${currency} ${amount.toFixed(2)}`;
  }

  /**
   * Validate payment amount
   */
  validateAmount(amount: number): boolean {
    // Minimum amount is 1 INR (100 paise)
    return amount >= 100 && amount <= 50000000; // Max 5 lakh INR
  }
}

// Export singleton instance
export const paymentService = new PaymentService();

// Export utility functions
export const processPayment = (options: PaymentOptions) => paymentService.processPayment(options);
export const subscribeToCommunity = (communityId: string, planType?: 'monthly' | 'yearly') => 
  paymentService.subscribeToCommunity(communityId, planType);
export const purchaseCourse = (courseId: string) => paymentService.purchaseCourse(courseId);
export const getPaymentHistory = (page?: number, limit?: number) => 
  paymentService.getPaymentHistory(page, limit);
export const formatAmount = (amount: number, currency?: string) => 
  paymentService.formatAmount(amount, currency);
