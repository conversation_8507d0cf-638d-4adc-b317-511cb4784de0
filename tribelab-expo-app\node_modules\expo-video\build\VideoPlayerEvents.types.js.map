{"version": 3, "file": "VideoPlayerEvents.types.js", "sourceRoot": "", "sources": ["../src/VideoPlayerEvents.types.ts"], "names": [], "mappings": "", "sourcesContent": ["import {\n  PlayerError,\n  SubtitleTrack,\n  VideoPlayerStatus,\n  VideoSource,\n  VideoTrack,\n  AudioTrack,\n} from './VideoPlayer.types';\n\n/**\n * Handlers for events which can be emitted by the player.\n */\nexport type VideoPlayerEvents = {\n  /**\n   * Handler for an event emitted when the status of the player changes.\n   */\n  statusChange(payload: StatusChangeEventPayload): void;\n\n  /**\n   * Handler for an event emitted when the player starts or stops playback.\n   */\n  playingChange(payload: PlayingChangeEventPayload): void;\n\n  /**\n   * Handler for an event emitted when the `playbackRate` property of the player changes.\n   */\n  playbackRateChange(payload: PlaybackRateChangeEventPayload): void;\n\n  /**\n   * Handler for an event emitted when the `volume` of `muted` property of the player changes.\n   */\n  volumeChange(payload: VolumeChangeEventPayload): void;\n\n  /**\n   * Handler for an event emitted when the `muted` property of the player changes\n   */\n  mutedChange(payload: MutedChangeEventPayload): void;\n\n  /**\n   * Handler for an event emitted when the player plays to the end of the current source.\n   */\n  playToEnd(): void;\n\n  /**\n   * Handler for an event emitted in a given interval specified by the `timeUpdateEventInterval`.\n   */\n  timeUpdate(payload: TimeUpdateEventPayload): void;\n\n  /**\n   * Handler for an event emitted when the current media source of the player changes.\n   */\n  sourceChange(payload: SourceChangeEventPayload): void;\n\n  /**\n   * Handler for an event emitted when the available subtitle tracks change.\n   */\n  availableSubtitleTracksChange(payload: AvailableSubtitleTracksChangeEventPayload): void;\n\n  /**\n   * Handler for an event emitted when the current subtitle track changes.\n   */\n  subtitleTrackChange(payload: SubtitleTrackChangeEventPayload): void;\n\n  /**\n   * Handler for an event emitted when the available audio tracks change.\n   */\n  availableAudioTracksChange(payload: AvailableAudioTracksChangeEventPayload): void;\n\n  /**\n   * Handler for an event emitted when the current audio track changes.\n   */\n  audioTrackChange(payload: AudioTrackChangeEventPayload): void;\n\n  /**\n   * Handler for an event emitted when the current video track changes.\n   */\n  videoTrackChange(payload: VideoTrackChangeEventPayload): void;\n\n  /**\n   * Handler for an event emitted when the player has finished loading metadata for the current video source.\n   * This event is emitted when the player has finished metadata for a [`VideoSource`](#videosource), but it doesn't mean that there is enough data buffered to start the playback.\n   */\n  sourceLoad(payload: SourceLoadEventPayload): void;\n};\n\n/**\n * Data delivered with the [`statusChange`](#videoplayerevents) event.\n */\nexport type StatusChangeEventPayload = {\n  /**\n   * New status of the player.\n   */\n  status: VideoPlayerStatus;\n\n  /**\n   * Previous status of the player.\n   */\n  oldStatus?: VideoPlayerStatus;\n\n  /**\n   * Error object containing information about the error that occurred.\n   */\n  error?: PlayerError;\n};\n\n/**\n * Data delivered with the [`playingChange`](#videoplayerevents) event.\n */\nexport type PlayingChangeEventPayload = {\n  /**\n   * Boolean value whether the player is currently playing.\n   */\n  isPlaying: boolean;\n\n  /**\n   * Previous value of the `isPlaying` property.\n   */\n  oldIsPlaying?: boolean;\n};\n\n/**\n * Data delivered with the [`playbackRateChange`](#videoplayerevents) event.\n */\nexport type PlaybackRateChangeEventPayload = {\n  /**\n   * Float value indicating the current playback speed of the player.\n   */\n  playbackRate: number;\n\n  /**\n   * Previous value of the `playbackRate` property.\n   */\n  oldPlaybackRate?: number;\n};\n\n/**\n * Data delivered with the [`volumeChange`](#videoplayerevents) event.\n */\nexport type VolumeChangeEventPayload = {\n  /**\n   * Float value indicating the current volume of the player.\n   */\n  volume: number;\n\n  /**\n   * Previous value of the `volume` property.\n   */\n  oldVolume?: number;\n};\n\n/**\n * Data delivered with the [`mutedChange`](#videoplayerevents) event.\n */\nexport type MutedChangeEventPayload = {\n  /**\n   * Boolean value whether the player is currently muted.\n   */\n  muted: boolean;\n\n  /**\n   * Previous value of the `isMuted` property.\n   */\n  oldMuted?: boolean;\n};\n\n/**\n * Data delivered with the [`sourceChange`](#videoplayerevents) event.\n */\nexport type SourceChangeEventPayload = {\n  /**\n   * New source of the player.\n   */\n  source: VideoSource;\n\n  /**\n   * Previous source of the player.\n   */\n  oldSource?: VideoSource;\n};\n\n/**\n * Data delivered with the [`timeUpdate`](#videoplayerevents) event, contains information about the current playback progress.\n */\nexport type TimeUpdateEventPayload = {\n  /**\n   * Float value indicating the current playback time in seconds. Same as the [`currentTime`](#currenttime) property.\n   */\n  currentTime: number;\n\n  /**\n   * The exact timestamp when the currently displayed video frame was sent from the server,\n   * based on the `EXT-X-PROGRAM-DATE-TIME` tag in the livestream metadata.\n   * Same as the [`currentLiveTimestamp`](#currentlivetimestamp) property.\n   * @platform android\n   * @platform ios\n   */\n  currentLiveTimestamp: number | null;\n\n  /**\n   * Float value indicating the latency of the live stream in seconds.\n   * Same as the [`currentOffsetFromLive`](#currentoffsetfromlive) property.\n   * @platform android\n   * @platform ios\n   */\n  currentOffsetFromLive: number | null;\n\n  /**\n   * Float value indicating how far the player has buffered the video in seconds.\n   * Same as the [`bufferedPosition`](#bufferedPosition) property.\n   * @platform android\n   * @platform ios\n   */\n  bufferedPosition: number;\n};\n\nexport type SubtitleTrackChangeEventPayload = {\n  /**\n   * New subtitle track of the player.\n   */\n  subtitleTrack: SubtitleTrack | null;\n\n  /**\n   * Previous subtitle track of the player.\n   */\n  oldSubtitleTrack?: SubtitleTrack | null;\n};\n\n/**\n * Data delivered with the [`videoTrackChange`](#videoplayerevents) event, contains information about the video track which is currently being played.\n */\nexport type VideoTrackChangeEventPayload = {\n  /**\n   * New video track of the player.\n   */\n  videoTrack: VideoTrack | null;\n\n  /**\n   * Previous video track of the player.\n   */\n  oldVideoTrack?: VideoTrack | null;\n};\n\n/**\n * TODO @behenate: For SDK53 mark as deprecated in favor of SourceLoadEventPayload\n * @hidden\n */\nexport type AvailableSubtitleTracksChangeEventPayload = {\n  /**\n   * Array of available subtitle tracks.\n   */\n  availableSubtitleTracks: SubtitleTrack[];\n\n  /**\n   * Previous array of available subtitle tracks.\n   */\n  oldAvailableSubtitleTracks?: SubtitleTrack[];\n};\n\n/**\n * Data delivered with the [`sourceLoad`](#videoplayerevents) event, contains information about the video source that has finished loading.\n */\nexport type SourceLoadEventPayload = {\n  /**\n   * The video source that has been loaded.\n   */\n  videoSource: VideoSource | null;\n\n  /**\n   * Duration of the video source in seconds.\n   */\n  duration: number;\n\n  /**\n   * Video tracks available for the loaded video source.\n   *\n   * > On iOS, when using a HLS source, make sure that the uri contains `.m3u8` extension or that the [`contentType`](#contenttype) property of the [`VideoSource`](#videosource) has been set to `'hls'`. Otherwise, the video tracks will not be available.\n   */\n  availableVideoTracks: VideoTrack[];\n\n  /**\n   * Subtitle tracks available for the loaded video source.\n   */\n  availableSubtitleTracks: SubtitleTrack[];\n\n  /**\n   * Audio tracks available for the loaded video source.\n   */\n  availableAudioTracks: AudioTrack[];\n};\n\ntype AudioTrackChangeEventPayload = {\n  /**\n   * New audio track of the player.\n   */\n  audioTrack: AudioTrack | null;\n\n  /**\n   * Previous audio track of the player.\n   */\n  oldAudioTrack?: AudioTrack | null;\n};\n\ntype AvailableAudioTracksChangeEventPayload = {\n  /**\n   * Array of available audio tracks.\n   */\n  availableAudioTracks: AudioTrack[];\n\n  /**\n   * Previous array of available audio tracks.\n   */\n  oldAvailableAudioTracks?: AudioTrack[];\n};\n"]}