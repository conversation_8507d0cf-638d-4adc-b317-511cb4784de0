import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Modal,
} from 'react-native';
import { Image } from 'expo-image';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import {
  pickImageFromCamera,
  pickImageFromGallery,
  uploadImageToR2,
  ImagePickerOptions,
  validateImage,
  getOptimizedImageUrl,
} from '../utils/r2Storage';

interface ImageUploadProps {
  currentImageUrl?: string;
  onImageUploaded: (imageUrl: string, fileName: string) => void;
  uploadType?: 'profile' | 'banner' | 'thumbnail' | 'general';
  style?: any;
  size?: number;
  borderRadius?: number;
  showUploadButton?: boolean;
  disabled?: boolean;
  placeholder?: string;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  currentImageUrl,
  onImageUploaded,
  uploadType = 'general',
  style,
  size = 100,
  borderRadius = 50,
  showUploadButton = true,
  disabled = false,
  placeholder = 'Tap to upload image',
}) => {
  const [uploading, setUploading] = useState(false);
  const [showPicker, setShowPicker] = useState(false);

  const handleImagePicker = () => {
    if (disabled) return;
    setShowPicker(true);
  };

  const pickFromCamera = async () => {
    setShowPicker(false);
    try {
      const options = uploadType === 'profile' 
        ? ImagePickerOptions.profile 
        : uploadType === 'banner'
        ? ImagePickerOptions.banner
        : ImagePickerOptions.general;

      const result = await pickImageFromCamera(options);
      
      if (!result.canceled && result.assets && result.assets.length > 0) {
        await handleImageUpload(result);
      }
    } catch (error) {
      console.error('Camera picker error:', error);
      Alert.alert('Error', 'Failed to open camera. Please try again.');
    }
  };

  const pickFromGallery = async () => {
    setShowPicker(false);
    try {
      const options = uploadType === 'profile' 
        ? ImagePickerOptions.profile 
        : uploadType === 'banner'
        ? ImagePickerOptions.banner
        : ImagePickerOptions.general;

      const result = await pickImageFromGallery(options);
      
      if (!result.canceled && result.assets && result.assets.length > 0) {
        await handleImageUpload(result);
      }
    } catch (error) {
      console.error('Gallery picker error:', error);
      Alert.alert('Error', 'Failed to open gallery. Please try again.');
    }
  };

  const handleImageUpload = async (imageResult: ImagePicker.ImagePickerResult) => {
    try {
      setUploading(true);

      // Validate image
      if (!validateImage(imageResult)) {
        Alert.alert('Error', 'Please select a valid image.');
        return;
      }

      const asset = imageResult.assets[0];
      
      // Upload to R2
      const uploadResult = await uploadImageToR2(asset.uri, uploadType);
      
      // Call the callback with the uploaded image URL
      onImageUploaded(uploadResult.url, uploadResult.fileName);
      
      Alert.alert('Success', 'Image uploaded successfully!');

    } catch (error) {
      console.error('Upload error:', error);
      Alert.alert(
        'Upload Failed', 
        error instanceof Error ? error.message : 'Failed to upload image. Please try again.'
      );
    } finally {
      setUploading(false);
    }
  };

  const renderImagePickerModal = () => (
    <Modal
      visible={showPicker}
      transparent
      animationType="slide"
      onRequestClose={() => setShowPicker(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <Text style={styles.modalTitle}>Select Image</Text>
          
          <TouchableOpacity style={styles.modalOption} onPress={pickFromCamera}>
            <Ionicons name="camera" size={24} color="#007AFF" />
            <Text style={styles.modalOptionText}>Take Photo</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.modalOption} onPress={pickFromGallery}>
            <Ionicons name="images" size={24} color="#007AFF" />
            <Text style={styles.modalOptionText}>Choose from Gallery</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.modalOption, styles.cancelOption]} 
            onPress={() => setShowPicker(false)}
          >
            <Ionicons name="close" size={24} color="#FF3B30" />
            <Text style={[styles.modalOptionText, styles.cancelText]}>Cancel</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );

  const imageUrl = currentImageUrl ? getOptimizedImageUrl(currentImageUrl, {
    width: size * 2, // 2x for retina displays
    height: size * 2,
    quality: 85,
    format: 'webp'
  }) : null;

  return (
    <View style={[styles.container, style]}>
      <TouchableOpacity
        style={[
          styles.imageContainer,
          {
            width: size,
            height: size,
            borderRadius: borderRadius,
          },
          disabled && styles.disabled,
        ]}
        onPress={handleImagePicker}
        disabled={disabled || uploading}
      >
        {imageUrl ? (
          <Image
            source={{ uri: imageUrl }}
            style={[
              styles.image,
              {
                width: size,
                height: size,
                borderRadius: borderRadius,
              },
            ]}
            contentFit="cover"
            transition={200}
          />
        ) : (
          <View
            style={[
              styles.placeholder,
              {
                width: size,
                height: size,
                borderRadius: borderRadius,
              },
            ]}
          >
            <Ionicons 
              name="camera" 
              size={size * 0.3} 
              color="#999" 
            />
            {size > 80 && (
              <Text style={styles.placeholderText} numberOfLines={2}>
                {placeholder}
              </Text>
            )}
          </View>
        )}

        {uploading && (
          <View style={styles.uploadingOverlay}>
            <ActivityIndicator size="large" color="#007AFF" />
          </View>
        )}

        {showUploadButton && !uploading && (
          <View style={styles.uploadButton}>
            <Ionicons name="add" size={20} color="white" />
          </View>
        )}
      </TouchableOpacity>

      {renderImagePickerModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  imageContainer: {
    position: 'relative',
    backgroundColor: '#f5f5f5',
    borderWidth: 2,
    borderColor: '#e0e0e0',
    borderStyle: 'dashed',
  },
  image: {
    backgroundColor: '#f5f5f5',
  },
  placeholder: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f9f9f9',
    padding: 10,
  },
  placeholderText: {
    fontSize: 12,
    color: '#999',
    textAlign: 'center',
    marginTop: 5,
  },
  uploadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 50,
  },
  uploadButton: {
    position: 'absolute',
    bottom: -5,
    right: -5,
    backgroundColor: '#007AFF',
    borderRadius: 15,
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'white',
  },
  disabled: {
    opacity: 0.5,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    paddingBottom: 40,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  modalOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    paddingHorizontal: 10,
    borderRadius: 10,
    marginBottom: 10,
    backgroundColor: '#f8f9fa',
  },
  modalOptionText: {
    fontSize: 16,
    marginLeft: 15,
    color: '#333',
  },
  cancelOption: {
    backgroundColor: '#fff5f5',
    marginTop: 10,
  },
  cancelText: {
    color: '#FF3B30',
  },
});

export default ImageUpload;
