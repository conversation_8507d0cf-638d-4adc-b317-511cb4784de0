#!/usr/bin/env node

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset', prefix = '') {
  const timestamp = new Date().toLocaleTimeString();
  console.log(`${colors[color]}[${timestamp}]${prefix ? ` [${prefix}]` : ''} ${message}${colors.reset}`);
}

function checkBackendDependencies() {
  const backendPath = join(__dirname, 'backend');
  const nodeModulesPath = join(backendPath, 'node_modules');
  
  if (!fs.existsSync(nodeModulesPath)) {
    log('⚠️  Backend dependencies not found. Installing...', 'yellow');
    
    return new Promise((resolve, reject) => {
      const install = spawn('npm', ['install'], { 
        cwd: backendPath,
        stdio: 'inherit'
      });
      
      install.on('close', (code) => {
        if (code === 0) {
          log('✅ Backend dependencies installed successfully', 'green');
          resolve(true);
        } else {
          log('❌ Failed to install backend dependencies', 'red');
          reject(new Error('Backend dependency installation failed'));
        }
      });
    });
  }
  
  return Promise.resolve(true);
}

function startBackend() {
  return new Promise((resolve, reject) => {
    log('🚀 Starting backend server...', 'cyan', 'BACKEND');
    
    const backend = spawn('npm', ['run', 'dev'], {
      cwd: join(__dirname, 'backend'),
      stdio: 'pipe'
    });
    
    backend.stdout.on('data', (data) => {
      const output = data.toString().trim();
      if (output) {
        log(output, 'blue', 'BACKEND');
      }
    });
    
    backend.stderr.on('data', (data) => {
      const output = data.toString().trim();
      if (output && !output.includes('ExperimentalWarning')) {
        log(output, 'red', 'BACKEND');
      }
    });
    
    backend.on('error', (err) => {
      log(`Failed to start backend: ${err.message}`, 'red', 'BACKEND');
      reject(err);
    });
    
    // Wait for backend to be ready
    let backendReady = false;
    backend.stdout.on('data', (data) => {
      const output = data.toString();
      if (output.includes('TribeLab Mobile Backend running on port') && !backendReady) {
        backendReady = true;
        log('✅ Backend server is ready!', 'green', 'BACKEND');
        resolve(backend);
      }
    });
    
    // Timeout after 30 seconds
    setTimeout(() => {
      if (!backendReady) {
        log('⚠️  Backend startup timeout, but continuing...', 'yellow', 'BACKEND');
        resolve(backend);
      }
    }, 30000);
  });
}

function startExpo(backendProcess) {
  return new Promise((resolve, reject) => {
    log('📱 Starting Expo development server...', 'cyan', 'EXPO');
    
    const expo = spawn('npx', ['expo', 'start'], {
      cwd: __dirname,
      stdio: 'pipe'
    });
    
    expo.stdout.on('data', (data) => {
      const output = data.toString().trim();
      if (output) {
        log(output, 'magenta', 'EXPO');
      }
    });
    
    expo.stderr.on('data', (data) => {
      const output = data.toString().trim();
      if (output && !output.includes('ExperimentalWarning')) {
        log(output, 'red', 'EXPO');
      }
    });
    
    expo.on('error', (err) => {
      log(`Failed to start Expo: ${err.message}`, 'red', 'EXPO');
      reject(err);
    });
    
    expo.on('close', (code) => {
      log(`Expo server stopped with code ${code}`, 'yellow', 'EXPO');
      if (backendProcess && !backendProcess.killed) {
        log('🛑 Stopping backend server...', 'yellow', 'BACKEND');
        backendProcess.kill('SIGINT');
      }
    });
    
    resolve(expo);
  });
}

async function startDevelopment() {
  log('🎯 TribeLab Mobile Development Environment', 'bright');
  log('=' .repeat(60), 'cyan');
  log('🔧 This will start both the backend server and Expo app', 'cyan');
  log('=' .repeat(60), 'cyan');
  
  try {
    // Check and install backend dependencies
    await checkBackendDependencies();
    
    // Start backend server
    const backendProcess = await startBackend();
    
    // Wait a bit for backend to fully initialize
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Start Expo development server
    const expoProcess = await startExpo(backendProcess);
    
    log('🎉 Development environment is ready!', 'green');
    log('📱 Expo: Scan QR code with Expo Go app', 'cyan');
    log('🔗 Backend: http://localhost:4000', 'cyan');
    log('🏥 Health check: http://localhost:4000/health', 'cyan');
    log('', 'reset');
    log('💡 Press Ctrl+C to stop both servers', 'yellow');
    
    // Handle process termination
    const cleanup = () => {
      log('\n🛑 Shutting down development environment...', 'yellow');
      
      if (expoProcess && !expoProcess.killed) {
        expoProcess.kill('SIGINT');
      }
      
      if (backendProcess && !backendProcess.killed) {
        backendProcess.kill('SIGINT');
      }
      
      setTimeout(() => {
        process.exit(0);
      }, 2000);
    };
    
    process.on('SIGINT', cleanup);
    process.on('SIGTERM', cleanup);
    
    // Keep the process alive
    await new Promise(() => {});
    
  } catch (error) {
    log(`❌ Failed to start development environment: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Start the development environment
startDevelopment();
