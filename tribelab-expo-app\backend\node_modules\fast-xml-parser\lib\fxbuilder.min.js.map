{"version": 3, "file": "./lib/fxbuilder.min.js", "mappings": "CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAoB,WAAID,IAExBD,EAAiB,WAAIC,GACtB,CATD,CASGK,MAAM,I,mBCRT,IAAIC,EAAsB,CCA1BA,EAAwB,CAACL,EAASM,KACjC,IAAI,IAAIC,KAAOD,EACXD,EAAoBG,EAAEF,EAAYC,KAASF,EAAoBG,EAAER,EAASO,IAC5EE,OAAOC,eAAeV,EAASO,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,IAE1E,ECNDF,EAAwB,CAACQ,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,GCClFT,EAAyBL,IACH,oBAAXkB,QAA0BA,OAAOC,aAC1CV,OAAOC,eAAeV,EAASkB,OAAOC,YAAa,CAAEC,MAAO,WAE7DX,OAAOC,eAAeV,EAAS,aAAc,CAAEoB,OAAO,GAAO,G,KCG/C,SAASC,EAAMC,EAAQC,GAClC,IAAIC,EAAc,GAIlB,OAHID,EAAQE,QAAUF,EAAQG,SAASC,OAAS,IAC5CH,EAXI,MAaDI,EAASN,EAAQC,EAAS,GAAIC,EACzC,CAEA,SAASI,EAASC,EAAKN,EAASO,EAAON,GAInC,IAHA,IAAIO,EAAS,GACTC,GAAuB,EAElBC,EAAI,EAAGA,EAAIJ,EAAIF,OAAQM,IAAK,CACjC,IAAMC,EAASL,EAAII,GACbE,EAAUC,EAASF,GACzB,QAAeG,IAAZF,EAAH,CAEA,IAAIG,EAIJ,GAHwBA,EAAH,IAAjBR,EAAMH,OAAyBQ,EAChBL,EAAK,IAAIK,EAExBA,IAAYZ,EAAQgB,aAYjB,GAAIJ,IAAYZ,EAAQiB,cAOxB,GAAIL,IAAYZ,EAAQkB,gBAIxB,GAAmB,MAAfN,EAAQ,GAAZ,CASP,IAAIO,EAAgBlB,EACE,KAAlBkB,IACAA,GAAiBnB,EAAQG,UAE7B,IACMiB,EAAWnB,EAAW,IAAOW,EADpBS,EAAYV,EAAO,MAAOX,GAEnCsB,EAAWjB,EAASM,EAAOC,GAAUZ,EAASe,EAAUI,IACf,IAA3CnB,EAAQuB,aAAaC,QAAQZ,GACzBZ,EAAQyB,qBAAsBjB,GAAUY,EAAW,IAClDZ,GAAUY,EAAW,KACjBE,GAAgC,IAApBA,EAASlB,SAAiBJ,EAAQ0B,kBAEhDJ,GAAYA,EAASK,SAAS,KACrCnB,GAAUY,EAAQ,IAAOE,EAAWrB,EAAW,KAAKW,EAAO,KAE3DJ,GAAUY,EAAW,IACjBE,GAA4B,KAAhBrB,IAAuBqB,EAASM,SAAS,OAASN,EAASM,SAAS,OAChFpB,GAAUP,EAAcD,EAAQG,SAAWmB,EAAWrB,EAEtDO,GAAUc,EAEdd,GAAM,KAASI,EAAO,KAVtBJ,GAAUY,EAAW,KAYzBX,GAAuB,CAxBvB,KARO,CACH,IAAMoB,EAASR,EAAYV,EAAO,MAAOX,GACnC8B,EAAsB,SAAZlB,EAAqB,GAAKX,EACtC8B,EAAiBpB,EAAOC,GAAS,GAAGZ,EAAQgB,cAEhDR,GAAUsB,EAAO,IAAOlB,GADxBmB,EAA2C,IAA1BA,EAAe3B,OAAe,IAAM2B,EAAiB,IACnBF,EAAM,KACzDpB,GAAuB,CAE3B,MAXID,GAAUP,EAAW,UAAUU,EAAOC,GAAS,GAAGZ,EAAQgB,cAAa,SACvEP,GAAuB,OARnBA,IACAD,GAAUP,GAEdO,GAAM,YAAgBG,EAAOC,GAAS,GAAGZ,EAAQgB,cAAa,MAC9DP,GAAuB,MAjB3B,CACI,IAAIuB,EAAUrB,EAAOC,GAChBqB,EAAWlB,EAAUf,KAEtBgC,EAAUE,EADVF,EAAUhC,EAAQmC,kBAAkBvB,EAASoB,GACLhC,IAExCS,IACAD,GAAUP,GAEdO,GAAUwB,EACVvB,GAAuB,CAqB3B,CArCkC,CA8DtC,CAEA,OAAOD,CACX,CAEA,SAASK,EAASvB,GAEd,IADA,IAAM8C,EAAOlD,OAAOkD,KAAK9C,GAChBoB,EAAI,EAAGA,EAAI0B,EAAKhC,OAAQM,IAAK,CAClC,IAAM1B,EAAMoD,EAAK1B,GACjB,GAAIpB,EAAIG,eAAeT,IACX,OAARA,EAAc,OAAOA,CAC7B,CACJ,CAEA,SAASqC,EAAYgB,EAASrC,GAC1B,IAAIsC,EAAU,GACd,GAAID,IAAYrC,EAAQuC,iBACpB,IAAK,IAAIC,KAAQH,EACb,GAAIA,EAAQ5C,eAAe+C,GAA3B,CACA,IAAIC,EAAUzC,EAAQ0C,wBAAwBF,EAAMH,EAAQG,KAE5C,KADhBC,EAAUP,EAAqBO,EAASzC,KAChBA,EAAQ2C,0BAC5BL,GAAO,IAAQE,EAAKI,OAAO5C,EAAQ6C,oBAAoBzC,QAEvDkC,GAAO,IAAQE,EAAKI,OAAO5C,EAAQ6C,oBAAoBzC,QAAO,KAAKqC,EAAO,GANpC,CAUlD,OAAOH,CACX,CAEA,SAASL,EAAW1B,EAAOP,GAEvB,IAAIY,GADJL,EAAQA,EAAMqC,OAAO,EAAGrC,EAAMH,OAASJ,EAAQgB,aAAaZ,OAAS,IACjDwC,OAAOrC,EAAMuC,YAAY,KAAO,GACpD,IAAK,IAAIC,KAAS/C,EAAQgD,UACtB,GAAIhD,EAAQgD,UAAUD,KAAWxC,GAASP,EAAQgD,UAAUD,KAAW,KAAOnC,EAAS,OAAO,EAElG,OAAO,CACX,CAEA,SAASsB,EAAqBe,EAAWjD,GACrC,GAAIiD,GAAaA,EAAU7C,OAAS,GAAKJ,EAAQkD,gBAC7C,IAAK,IAAIxC,EAAI,EAAGA,EAAIV,EAAQmD,SAAS/C,OAAQM,IAAK,CAC9C,IAAM0C,EAASpD,EAAQmD,SAASzC,GAChCuC,EAAYA,EAAUI,QAAQD,EAAOE,MAAOF,EAAOG,IACvD,CAEJ,OAAON,CACX,C,oIChIA,IAAMO,EAAiB,CACrBX,oBAAqB,KACrBY,qBAAqB,EACrBzC,aAAc,QACduB,kBAAkB,EAClBtB,eAAe,EACff,QAAQ,EACRC,SAAU,KACVuB,mBAAmB,EACnBD,sBAAsB,EACtBkB,2BAA2B,EAC3BR,kBAAmB,SAASnD,EAAK0E,GAC/B,OAAOA,CACT,EACAhB,wBAAyB,SAASiB,EAAUD,GAC1C,OAAOA,CACT,EACAE,eAAe,EACf1C,iBAAiB,EACjBK,aAAc,GACd4B,SAAU,CACR,CAAEG,MAAO,IAAIO,OAAO,IAAK,KAAMN,IAAK,SACpC,CAAED,MAAO,IAAIO,OAAO,IAAK,KAAMN,IAAK,QACpC,CAAED,MAAO,IAAIO,OAAO,IAAK,KAAMN,IAAK,QACpC,CAAED,MAAO,IAAIO,OAAO,IAAM,KAAMN,IAAK,UACrC,CAAED,MAAO,IAAIO,OAAO,IAAM,KAAMN,IAAK,WAEvCL,iBAAiB,EACjBF,UAAW,GAGXc,cAAc,GAGD,SAASC,EAAQ/D,GCvCjB,IAA+BuC,EDwC5C1D,KAAKmB,QAAUd,OAAO8E,OAAO,CAAC,EAAGR,EAAgBxD,IACX,IAAlCnB,KAAKmB,QAAQuC,kBAA6B1D,KAAKmB,QAAQyD,oBACzD5E,KAAKoF,YAAc,WACjB,OAAO,CACT,GAEApF,KAAKqF,mBC7C2B,mBADU3B,ED8CM1D,KAAKmB,QAAQuC,kBC5ClDA,EAEP4B,MAAMC,QAAQ7B,GACP,SAACoB,GACJ,QAAsCU,EAAtCC,E,4rBAAAC,CAAsBhC,KAAgB8B,EAAAC,KAAAE,MAAE,CAAC,IAA9BC,EAAOJ,EAAAxE,MACd,GAAuB,iBAAZ4E,GAAwBd,IAAac,EAC5C,OAAO,EAEX,GAAIA,aAAmBZ,QAAUY,EAAQC,KAAKf,GAC1C,OAAO,CAEf,CACJ,EAEG,kBAAM,CAAK,ED+BlB9E,KAAK8F,cAAgB9F,KAAKmB,QAAQ6C,oBAAoBzC,OACtDvB,KAAKoF,YAAcA,GAGrBpF,KAAK+F,qBAAuBA,EAExB/F,KAAKmB,QAAQE,QACfrB,KAAKgG,UAAYA,EACjBhG,KAAKiG,WAAa,MAClBjG,KAAKkG,QAAU,OAEflG,KAAKgG,UAAY,WACf,MAAO,EACT,EACAhG,KAAKiG,WAAa,IAClBjG,KAAKkG,QAAU,GAEnB,CAmHA,SAASH,EAAsBI,EAAQhG,EAAKiG,EAAOC,GACjD,IAAMC,EAAStG,KAAKuG,IAAIJ,EAAQC,EAAQ,EAAGC,EAAOG,OAAOrG,IACzD,YAA0C8B,IAAtCkE,EAAOnG,KAAKmB,QAAQgB,eAA8D,IAA/B9B,OAAOkD,KAAK4C,GAAQ5E,OAClEvB,KAAKyG,iBAAiBN,EAAOnG,KAAKmB,QAAQgB,cAAehC,EAAKmG,EAAO7C,QAAS2C,GAE9EpG,KAAK0G,gBAAgBJ,EAAO5B,IAAKvE,EAAKmG,EAAO7C,QAAS2C,EAEjE,CAuFA,SAASJ,EAAUI,GACjB,OAAOpG,KAAKmB,QAAQG,SAASqF,OAAOP,EACtC,CAEA,SAAShB,EAAYwB,GACnB,SAAIA,EAAKC,WAAW7G,KAAKmB,QAAQ6C,sBAAwB4C,IAAS5G,KAAKmB,QAAQgB,eACtEyE,EAAK7C,OAAO/D,KAAK8F,cAI5B,C,OAzNAZ,EAAQvE,UAAUmG,MAAQ,SAASC,GACjC,OAAG/G,KAAKmB,QAAQ4D,cACPiC,EAAmBD,EAAM/G,KAAKmB,UAElCmE,MAAMC,QAAQwB,IAAS/G,KAAKmB,QAAQ8F,eAAiBjH,KAAKmB,QAAQ8F,cAAc1F,OAAS,KACtF2F,EAAA,IACDlH,KAAKmB,QAAQ8F,eAAiBF,EADjCA,EACqCG,GAGhClH,KAAKuG,IAAIQ,EAAM,EAAG,IAAIrC,KALkE,IAADwC,CAOlG,EAEAhC,EAAQvE,UAAU4F,IAAM,SAASQ,EAAMX,EAAOC,GAC5C,IAAI5C,EAAU,GACViB,EAAM,GACJhD,EAAQ2E,EAAOc,KAAK,KAC1B,IAAK,IAAIhH,KAAO4G,EACd,GAAI1G,OAAOM,UAAUC,eAAeC,KAAKkG,EAAM5G,GAC/C,QAAyB,IAAd4G,EAAK5G,GAEVH,KAAKoF,YAAYjF,KACnBuE,GAAO,SAEJ,GAAkB,OAAdqC,EAAK5G,GAEVH,KAAKoF,YAAYjF,IAEVA,IAAQH,KAAKmB,QAAQiB,cAD9BsC,GAAO,GAGa,MAAXvE,EAAI,GACbuE,GAAO1E,KAAKgG,UAAUI,GAAS,IAAMjG,EAAM,IAAMH,KAAKiG,WAEtDvB,GAAO1E,KAAKgG,UAAUI,GAAS,IAAMjG,EAAM,IAAMH,KAAKiG,gBAGnD,GAAIc,EAAK5G,aAAgBiH,KAC9B1C,GAAO1E,KAAKyG,iBAAiBM,EAAK5G,GAAMA,EAAK,GAAIiG,QAC5C,GAAyB,iBAAdW,EAAK5G,GAAmB,CAExC,IAAMwD,EAAO3D,KAAKoF,YAAYjF,GAC9B,GAAIwD,IAAS3D,KAAKqF,mBAAmB1B,EAAMjC,GACzC+B,GAAWzD,KAAKqH,iBAAiB1D,EAAM,GAAKoD,EAAK5G,SAC5C,IAAKwD,EAEV,GAAIxD,IAAQH,KAAKmB,QAAQgB,aAAc,CACrC,IAAImF,EAAStH,KAAKmB,QAAQmC,kBAAkBnD,EAAK,GAAK4G,EAAK5G,IAC3DuE,GAAO1E,KAAKqD,qBAAqBiE,EACnC,MACE5C,GAAO1E,KAAKyG,iBAAiBM,EAAK5G,GAAMA,EAAK,GAAIiG,EAGvD,MAAO,GAAId,MAAMC,QAAQwB,EAAK5G,IAAO,CAKnC,IAHA,IAAMoH,EAASR,EAAK5G,GAAKoB,OACrBiG,EAAa,GACbC,EAAc,GACTC,EAAI,EAAGA,EAAIH,EAAQG,IAAK,CAC/B,IAAMC,EAAOZ,EAAK5G,GAAKuH,GACvB,QAAoB,IAATC,QAEJ,GAAa,OAATA,EACK,MAAXxH,EAAI,GAAYuE,GAAO1E,KAAKgG,UAAUI,GAAS,IAAMjG,EAAM,IAAMH,KAAKiG,WACpEvB,GAAO1E,KAAKgG,UAAUI,GAAS,IAAMjG,EAAM,IAAMH,KAAKiG,gBAEtD,GAAoB,iBAAT0B,EAChB,GAAG3H,KAAKmB,QAAQ8D,aAAa,CAC3B,IAAMqB,EAAStG,KAAKuG,IAAIoB,EAAMvB,EAAQ,EAAGC,EAAOG,OAAOrG,IACvDqH,GAAclB,EAAO5B,IACjB1E,KAAKmB,QAAQyD,qBAAuB+C,EAAK/G,eAAeZ,KAAKmB,QAAQyD,uBACvE6C,GAAenB,EAAO7C,QAE1B,MACE+D,GAAcxH,KAAK+F,qBAAqB4B,EAAMxH,EAAKiG,EAAOC,QAG5D,GAAIrG,KAAKmB,QAAQ8D,aAAc,CAC7B,IAAIb,EAAYpE,KAAKmB,QAAQmC,kBAAkBnD,EAAKwH,GAEpDH,GADApD,EAAYpE,KAAKqD,qBAAqBe,EAExC,MACEoD,GAAcxH,KAAKyG,iBAAiBkB,EAAMxH,EAAK,GAAIiG,EAGzD,CACGpG,KAAKmB,QAAQ8D,eACduC,EAAaxH,KAAK0G,gBAAgBc,EAAYrH,EAAKsH,EAAarB,IAElE1B,GAAO8C,CACT,MAEE,GAAIxH,KAAKmB,QAAQyD,qBAAuBzE,IAAQH,KAAKmB,QAAQyD,oBAG3D,IAFA,IAAMgD,EAAKvH,OAAOkD,KAAKwD,EAAK5G,IACtB0H,EAAID,EAAGrG,OACJmG,EAAI,EAAGA,EAAIG,EAAGH,IACrBjE,GAAWzD,KAAKqH,iBAAiBO,EAAGF,GAAI,GAAKX,EAAK5G,GAAKyH,EAAGF,UAG5DhD,GAAO1E,KAAK+F,qBAAqBgB,EAAK5G,GAAMA,EAAKiG,EAAOC,GAI9D,MAAO,CAAC5C,QAASA,EAASiB,IAAKA,EACjC,EAEAQ,EAAQvE,UAAU0G,iBAAmB,SAASvC,EAAUJ,GAGtD,OAFAA,EAAM1E,KAAKmB,QAAQ0C,wBAAwBiB,EAAU,GAAKJ,GAC1DA,EAAM1E,KAAKqD,qBAAqBqB,GAC5B1E,KAAKmB,QAAQ2C,2BAAqC,SAARY,EACrC,IAAMI,EACD,IAAMA,EAAW,KAAOJ,EAAM,GAC9C,EAWAQ,EAAQvE,UAAU+F,gBAAkB,SAAShC,EAAKvE,EAAKsD,EAAS2C,GAC9D,GAAW,KAAR1B,EACD,MAAc,MAAXvE,EAAI,GAAoBH,KAAKgG,UAAUI,GAAS,IAAMjG,EAAMsD,EAAS,IAAMzD,KAAKiG,WAE1EjG,KAAKgG,UAAUI,GAAS,IAAMjG,EAAMsD,EAAUzD,KAAK8H,SAAS3H,GAAOH,KAAKiG,WAIjF,IAAI8B,EAAY,KAAO5H,EAAMH,KAAKiG,WAC9B+B,EAAgB,GAQpB,MANc,MAAX7H,EAAI,KACL6H,EAAgB,IAChBD,EAAY,KAITtE,GAAuB,KAAZA,IAAyC,IAAtBiB,EAAI/B,QAAQ,MAEH,IAAjC3C,KAAKmB,QAAQkB,iBAA6BlC,IAAQH,KAAKmB,QAAQkB,iBAA4C,IAAzB2F,EAAczG,OAClGvB,KAAKgG,UAAUI,GAAM,UAAU1B,EAAG,SAAQ1E,KAAKkG,QAGpDlG,KAAKgG,UAAUI,GAAS,IAAMjG,EAAMsD,EAAUuE,EAAgBhI,KAAKiG,WACnEvB,EACA1E,KAAKgG,UAAUI,GAAS2B,EAPjB/H,KAAKgG,UAAUI,GAAS,IAAOjG,EAAMsD,EAAUuE,EAAgB,IAAMtD,EAAMqD,CAU1F,EAEA7C,EAAQvE,UAAUmH,SAAW,SAAS3H,GACpC,IAAI2H,EAAW,GAQf,OAP+C,IAA5C9H,KAAKmB,QAAQuB,aAAaC,QAAQxC,GAC/BH,KAAKmB,QAAQyB,uBAAsBkF,EAAW,KAElDA,EADO9H,KAAKmB,QAAQ0B,kBACT,IAEH,MAAS1C,EAEZ2H,CACT,EAcA5C,EAAQvE,UAAU8F,iBAAmB,SAAS/B,EAAKvE,EAAKsD,EAAS2C,GAC/D,IAAmC,IAA/BpG,KAAKmB,QAAQiB,eAA2BjC,IAAQH,KAAKmB,QAAQiB,cAC/D,OAAOpC,KAAKgG,UAAUI,GAAM,YAAe1B,EAAG,MAAS1E,KAAKkG,QACxD,IAAqC,IAAjClG,KAAKmB,QAAQkB,iBAA6BlC,IAAQH,KAAKmB,QAAQkB,gBACvE,OAAOrC,KAAKgG,UAAUI,GAAM,UAAU1B,EAAG,SAAS1E,KAAKkG,QACnD,GAAc,MAAX/F,EAAI,GACX,OAAQH,KAAKgG,UAAUI,GAAS,IAAMjG,EAAMsD,EAAS,IAAMzD,KAAKiG,WAEhE,IAAI7B,EAAYpE,KAAKmB,QAAQmC,kBAAkBnD,EAAKuE,GAGpD,MAAkB,MAFlBN,EAAYpE,KAAKqD,qBAAqBe,IAG7BpE,KAAKgG,UAAUI,GAAS,IAAMjG,EAAMsD,EAAUzD,KAAK8H,SAAS3H,GAAOH,KAAKiG,WAExEjG,KAAKgG,UAAUI,GAAS,IAAMjG,EAAMsD,EAAU,IAClDW,EACD,KAAOjE,EAAMH,KAAKiG,UAG1B,EAEAf,EAAQvE,UAAU0C,qBAAuB,SAASe,GAChD,GAAGA,GAAaA,EAAU7C,OAAS,GAAKvB,KAAKmB,QAAQkD,gBACnD,IAAK,IAAIxC,EAAE,EAAGA,EAAE7B,KAAKmB,QAAQmD,SAAS/C,OAAQM,IAAK,CACjD,IAAM0C,EAASvE,KAAKmB,QAAQmD,SAASzC,GACrCuC,EAAYA,EAAUI,QAAQD,EAAOE,MAAOF,EAAOG,IACrD,CAEF,OAAON,CACT,E", "sources": ["webpack://XMLBuilder/webpack/universalModuleDefinition", "webpack://XMLBuilder/webpack/bootstrap", "webpack://XMLBuilder/webpack/runtime/define property getters", "webpack://XMLBuilder/webpack/runtime/hasOwnProperty shorthand", "webpack://XMLBuilder/webpack/runtime/make namespace object", "webpack://XMLBuilder/./src/xmlbuilder/orderedJs2Xml.js", "webpack://XMLBuilder/./src/xmlbuilder/json2xml.js", "webpack://XMLBuilder/./src/ignoreAttributes.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"XMLBuilder\"] = factory();\n\telse\n\t\troot[\"XMLBuilder\"] = factory();\n})(this, () => {\nreturn ", "// The require scope\nvar __webpack_require__ = {};\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "const EOL = \"\\n\";\n\n/**\n * \n * @param {array} jArray \n * @param {any} options \n * @returns \n */\nexport default function toXml(jArray, options) {\n    let indentation = \"\";\n    if (options.format && options.indentBy.length > 0) {\n        indentation = EOL;\n    }\n    return arrToStr(jArray, options, \"\", indentation);\n}\n\nfunction arrToStr(arr, options, jPath, indentation) {\n    let xmlStr = \"\";\n    let isPreviousElementTag = false;\n\n    for (let i = 0; i < arr.length; i++) {\n        const tagObj = arr[i];\n        const tagName = propName(tagObj);\n        if(tagName === undefined) continue;\n\n        let newJPath = \"\";\n        if (jPath.length === 0) newJPath = tagName\n        else newJPath = `${jPath}.${tagName}`;\n\n        if (tagName === options.textNodeName) {\n            let tagText = tagObj[tagName];\n            if (!isStopNode(newJPath, options)) {\n                tagText = options.tagValueProcessor(tagName, tagText);\n                tagText = replaceEntitiesValue(tagText, options);\n            }\n            if (isPreviousElementTag) {\n                xmlStr += indentation;\n            }\n            xmlStr += tagText;\n            isPreviousElementTag = false;\n            continue;\n        } else if (tagName === options.cdataPropName) {\n            if (isPreviousElementTag) {\n                xmlStr += indentation;\n            }\n            xmlStr += `<![CDATA[${tagObj[tagName][0][options.textNodeName]}]]>`;\n            isPreviousElementTag = false;\n            continue;\n        } else if (tagName === options.commentPropName) {\n            xmlStr += indentation + `<!--${tagObj[tagName][0][options.textNodeName]}-->`;\n            isPreviousElementTag = true;\n            continue;\n        } else if (tagName[0] === \"?\") {\n            const attStr = attr_to_str(tagObj[\":@\"], options);\n            const tempInd = tagName === \"?xml\" ? \"\" : indentation;\n            let piTextNodeName = tagObj[tagName][0][options.textNodeName];\n            piTextNodeName = piTextNodeName.length !== 0 ? \" \" + piTextNodeName : \"\"; //remove extra spacing\n            xmlStr += tempInd + `<${tagName}${piTextNodeName}${attStr}?>`;\n            isPreviousElementTag = true;\n            continue;\n        }\n        let newIdentation = indentation;\n        if (newIdentation !== \"\") {\n            newIdentation += options.indentBy;\n        }\n        const attStr = attr_to_str(tagObj[\":@\"], options);\n        const tagStart = indentation + `<${tagName}${attStr}`;\n        const tagValue = arrToStr(tagObj[tagName], options, newJPath, newIdentation);\n        if (options.unpairedTags.indexOf(tagName) !== -1) {\n            if (options.suppressUnpairedNode) xmlStr += tagStart + \">\";\n            else xmlStr += tagStart + \"/>\";\n        } else if ((!tagValue || tagValue.length === 0) && options.suppressEmptyNode) {\n            xmlStr += tagStart + \"/>\";\n        } else if (tagValue && tagValue.endsWith(\">\")) {\n            xmlStr += tagStart + `>${tagValue}${indentation}</${tagName}>`;\n        } else {\n            xmlStr += tagStart + \">\";\n            if (tagValue && indentation !== \"\" && (tagValue.includes(\"/>\") || tagValue.includes(\"</\"))) {\n                xmlStr += indentation + options.indentBy + tagValue + indentation;\n            } else {\n                xmlStr += tagValue;\n            }\n            xmlStr += `</${tagName}>`;\n        }\n        isPreviousElementTag = true;\n    }\n\n    return xmlStr;\n}\n\nfunction propName(obj) {\n    const keys = Object.keys(obj);\n    for (let i = 0; i < keys.length; i++) {\n        const key = keys[i];\n        if(!obj.hasOwnProperty(key)) continue;\n        if (key !== \":@\") return key;\n    }\n}\n\nfunction attr_to_str(attrMap, options) {\n    let attrStr = \"\";\n    if (attrMap && !options.ignoreAttributes) {\n        for (let attr in attrMap) {\n            if(!attrMap.hasOwnProperty(attr)) continue;\n            let attrVal = options.attributeValueProcessor(attr, attrMap[attr]);\n            attrVal = replaceEntitiesValue(attrVal, options);\n            if (attrVal === true && options.suppressBooleanAttributes) {\n                attrStr += ` ${attr.substr(options.attributeNamePrefix.length)}`;\n            } else {\n                attrStr += ` ${attr.substr(options.attributeNamePrefix.length)}=\"${attrVal}\"`;\n            }\n        }\n    }\n    return attrStr;\n}\n\nfunction isStopNode(jPath, options) {\n    jPath = jPath.substr(0, jPath.length - options.textNodeName.length - 1);\n    let tagName = jPath.substr(jPath.lastIndexOf(\".\") + 1);\n    for (let index in options.stopNodes) {\n        if (options.stopNodes[index] === jPath || options.stopNodes[index] === \"*.\" + tagName) return true;\n    }\n    return false;\n}\n\nfunction replaceEntitiesValue(textValue, options) {\n    if (textValue && textValue.length > 0 && options.processEntities) {\n        for (let i = 0; i < options.entities.length; i++) {\n            const entity = options.entities[i];\n            textValue = textValue.replace(entity.regex, entity.val);\n        }\n    }\n    return textValue;\n}\n", "'use strict';\n//parse Empty Node as self closing node\nimport buildFromOrderedJs from './orderedJs2Xml.js';\nimport getIgnoreAttributesFn from \"../ignoreAttributes.js\";\n\nconst defaultOptions = {\n  attributeNamePrefix: '@_',\n  attributesGroupName: false,\n  textNodeName: '#text',\n  ignoreAttributes: true,\n  cdataPropName: false,\n  format: false,\n  indentBy: '  ',\n  suppressEmptyNode: false,\n  suppressUnpairedNode: true,\n  suppressBooleanAttributes: true,\n  tagValueProcessor: function(key, a) {\n    return a;\n  },\n  attributeValueProcessor: function(attrName, a) {\n    return a;\n  },\n  preserveOrder: false,\n  commentPropName: false,\n  unpairedTags: [],\n  entities: [\n    { regex: new RegExp(\"&\", \"g\"), val: \"&amp;\" },//it must be on top\n    { regex: new RegExp(\">\", \"g\"), val: \"&gt;\" },\n    { regex: new RegExp(\"<\", \"g\"), val: \"&lt;\" },\n    { regex: new RegExp(\"\\'\", \"g\"), val: \"&apos;\" },\n    { regex: new RegExp(\"\\\"\", \"g\"), val: \"&quot;\" }\n  ],\n  processEntities: true,\n  stopNodes: [],\n  // transformTagName: false,\n  // transformAttributeName: false,\n  oneListGroup: false\n};\n\nexport default function Builder(options) {\n  this.options = Object.assign({}, defaultOptions, options);\n  if (this.options.ignoreAttributes === true || this.options.attributesGroupName) {\n    this.isAttribute = function(/*a*/) {\n      return false;\n    };\n  } else {\n    this.ignoreAttributesFn = getIgnoreAttributesFn(this.options.ignoreAttributes)\n    this.attrPrefixLen = this.options.attributeNamePrefix.length;\n    this.isAttribute = isAttribute;\n  }\n\n  this.processTextOrObjNode = processTextOrObjNode\n\n  if (this.options.format) {\n    this.indentate = indentate;\n    this.tagEndChar = '>\\n';\n    this.newLine = '\\n';\n  } else {\n    this.indentate = function() {\n      return '';\n    };\n    this.tagEndChar = '>';\n    this.newLine = '';\n  }\n}\n\nBuilder.prototype.build = function(jObj) {\n  if(this.options.preserveOrder){\n    return buildFromOrderedJs(jObj, this.options);\n  }else {\n    if(Array.isArray(jObj) && this.options.arrayNodeName && this.options.arrayNodeName.length > 1){\n      jObj = {\n        [this.options.arrayNodeName] : jObj\n      }\n    }\n    return this.j2x(jObj, 0, []).val;\n  }\n};\n\nBuilder.prototype.j2x = function(jObj, level, ajPath) {\n  let attrStr = '';\n  let val = '';\n  const jPath = ajPath.join('.')\n  for (let key in jObj) {\n    if(!Object.prototype.hasOwnProperty.call(jObj, key)) continue;\n    if (typeof jObj[key] === 'undefined') {\n      // supress undefined node only if it is not an attribute\n      if (this.isAttribute(key)) {\n        val += '';\n      }\n    } else if (jObj[key] === null) {\n      // null attribute should be ignored by the attribute list, but should not cause the tag closing\n      if (this.isAttribute(key)) {\n        val += '';\n      } else if (key === this.options.cdataPropName) {\n        val += '';\n      } else if (key[0] === '?') {\n        val += this.indentate(level) + '<' + key + '?' + this.tagEndChar;\n      } else {\n        val += this.indentate(level) + '<' + key + '/' + this.tagEndChar;\n      }\n      // val += this.indentate(level) + '<' + key + '/' + this.tagEndChar;\n    } else if (jObj[key] instanceof Date) {\n      val += this.buildTextValNode(jObj[key], key, '', level);\n    } else if (typeof jObj[key] !== 'object') {\n      //premitive type\n      const attr = this.isAttribute(key);\n      if (attr && !this.ignoreAttributesFn(attr, jPath)) {\n        attrStr += this.buildAttrPairStr(attr, '' + jObj[key]);\n      } else if (!attr) {\n        //tag value\n        if (key === this.options.textNodeName) {\n          let newval = this.options.tagValueProcessor(key, '' + jObj[key]);\n          val += this.replaceEntitiesValue(newval);\n        } else {\n          val += this.buildTextValNode(jObj[key], key, '', level);\n        }\n      }\n    } else if (Array.isArray(jObj[key])) {\n      //repeated nodes\n      const arrLen = jObj[key].length;\n      let listTagVal = \"\";\n      let listTagAttr = \"\";\n      for (let j = 0; j < arrLen; j++) {\n        const item = jObj[key][j];\n        if (typeof item === 'undefined') {\n          // supress undefined node\n        } else if (item === null) {\n          if(key[0] === \"?\") val += this.indentate(level) + '<' + key + '?' + this.tagEndChar;\n          else val += this.indentate(level) + '<' + key + '/' + this.tagEndChar;\n          // val += this.indentate(level) + '<' + key + '/' + this.tagEndChar;\n        } else if (typeof item === 'object') {\n          if(this.options.oneListGroup){\n            const result = this.j2x(item, level + 1, ajPath.concat(key));\n            listTagVal += result.val;\n            if (this.options.attributesGroupName && item.hasOwnProperty(this.options.attributesGroupName)) {\n              listTagAttr += result.attrStr\n            }\n          }else{\n            listTagVal += this.processTextOrObjNode(item, key, level, ajPath)\n          }\n        } else {\n          if (this.options.oneListGroup) {\n            let textValue = this.options.tagValueProcessor(key, item);\n            textValue = this.replaceEntitiesValue(textValue);\n            listTagVal += textValue;\n          } else {\n            listTagVal += this.buildTextValNode(item, key, '', level);\n          }\n        }\n      }\n      if(this.options.oneListGroup){\n        listTagVal = this.buildObjectNode(listTagVal, key, listTagAttr, level);\n      }\n      val += listTagVal;\n    } else {\n      //nested node\n      if (this.options.attributesGroupName && key === this.options.attributesGroupName) {\n        const Ks = Object.keys(jObj[key]);\n        const L = Ks.length;\n        for (let j = 0; j < L; j++) {\n          attrStr += this.buildAttrPairStr(Ks[j], '' + jObj[key][Ks[j]]);\n        }\n      } else {\n        val += this.processTextOrObjNode(jObj[key], key, level, ajPath)\n      }\n    }\n  }\n  return {attrStr: attrStr, val: val};\n};\n\nBuilder.prototype.buildAttrPairStr = function(attrName, val){\n  val = this.options.attributeValueProcessor(attrName, '' + val);\n  val = this.replaceEntitiesValue(val);\n  if (this.options.suppressBooleanAttributes && val === \"true\") {\n    return ' ' + attrName;\n  } else return ' ' + attrName + '=\"' + val + '\"';\n}\n\nfunction processTextOrObjNode (object, key, level, ajPath) {\n  const result = this.j2x(object, level + 1, ajPath.concat(key));\n  if (object[this.options.textNodeName] !== undefined && Object.keys(object).length === 1) {\n    return this.buildTextValNode(object[this.options.textNodeName], key, result.attrStr, level);\n  } else {\n    return this.buildObjectNode(result.val, key, result.attrStr, level);\n  }\n}\n\nBuilder.prototype.buildObjectNode = function(val, key, attrStr, level) {\n  if(val === \"\"){\n    if(key[0] === \"?\") return  this.indentate(level) + '<' + key + attrStr+ '?' + this.tagEndChar;\n    else {\n      return this.indentate(level) + '<' + key + attrStr + this.closeTag(key) + this.tagEndChar;\n    }\n  }else{\n\n    let tagEndExp = '</' + key + this.tagEndChar;\n    let piClosingChar = \"\";\n    \n    if(key[0] === \"?\") {\n      piClosingChar = \"?\";\n      tagEndExp = \"\";\n    }\n  \n    // attrStr is an empty string in case the attribute came as undefined or null\n    if ((attrStr || attrStr === '') && val.indexOf('<') === -1) {\n      return ( this.indentate(level) + '<' +  key + attrStr + piClosingChar + '>' + val + tagEndExp );\n    } else if (this.options.commentPropName !== false && key === this.options.commentPropName && piClosingChar.length === 0) {\n      return this.indentate(level) + `<!--${val}-->` + this.newLine;\n    }else {\n      return (\n        this.indentate(level) + '<' + key + attrStr + piClosingChar + this.tagEndChar +\n        val +\n        this.indentate(level) + tagEndExp    );\n    }\n  }\n}\n\nBuilder.prototype.closeTag = function(key){\n  let closeTag = \"\";\n  if(this.options.unpairedTags.indexOf(key) !== -1){ //unpaired\n    if(!this.options.suppressUnpairedNode) closeTag = \"/\"\n  }else if(this.options.suppressEmptyNode){ //empty\n    closeTag = \"/\";\n  }else{\n    closeTag = `></${key}`\n  }\n  return closeTag;\n}\n\nfunction buildEmptyObjNode(val, key, attrStr, level) {\n  if (val !== '') {\n    return this.buildObjectNode(val, key, attrStr, level);\n  } else {\n    if(key[0] === \"?\") return  this.indentate(level) + '<' + key + attrStr+ '?' + this.tagEndChar;\n    else {\n      return  this.indentate(level) + '<' + key + attrStr + '/' + this.tagEndChar;\n      // return this.buildTagStr(level,key, attrStr);\n    }\n  }\n}\n\nBuilder.prototype.buildTextValNode = function(val, key, attrStr, level) {\n  if (this.options.cdataPropName !== false && key === this.options.cdataPropName) {\n    return this.indentate(level) + `<![CDATA[${val}]]>` +  this.newLine;\n  }else if (this.options.commentPropName !== false && key === this.options.commentPropName) {\n    return this.indentate(level) + `<!--${val}-->` +  this.newLine;\n  }else if(key[0] === \"?\") {//PI tag\n    return  this.indentate(level) + '<' + key + attrStr+ '?' + this.tagEndChar; \n  }else{\n    let textValue = this.options.tagValueProcessor(key, val);\n    textValue = this.replaceEntitiesValue(textValue);\n  \n    if( textValue === ''){\n      return this.indentate(level) + '<' + key + attrStr + this.closeTag(key) + this.tagEndChar;\n    }else{\n      return this.indentate(level) + '<' + key + attrStr + '>' +\n         textValue +\n        '</' + key + this.tagEndChar;\n    }\n  }\n}\n\nBuilder.prototype.replaceEntitiesValue = function(textValue){\n  if(textValue && textValue.length > 0 && this.options.processEntities){\n    for (let i=0; i<this.options.entities.length; i++) {\n      const entity = this.options.entities[i];\n      textValue = textValue.replace(entity.regex, entity.val);\n    }\n  }\n  return textValue;\n}\n\nfunction indentate(level) {\n  return this.options.indentBy.repeat(level);\n}\n\nfunction isAttribute(name /*, options*/) {\n  if (name.startsWith(this.options.attributeNamePrefix) && name !== this.options.textNodeName) {\n    return name.substr(this.attrPrefixLen);\n  } else {\n    return false;\n  }\n}\n\n", "export default function getIgnoreAttributesFn(ignoreAttributes) {\n    if (typeof ignoreAttributes === 'function') {\n        return ignoreAttributes\n    }\n    if (Array.isArray(ignoreAttributes)) {\n        return (attrName) => {\n            for (const pattern of ignoreAttributes) {\n                if (typeof pattern === 'string' && attrName === pattern) {\n                    return true\n                }\n                if (pattern instanceof RegExp && pattern.test(attrName)) {\n                    return true\n                }\n            }\n        }\n    }\n    return () => false\n}"], "names": ["root", "factory", "exports", "module", "define", "amd", "this", "__webpack_require__", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "Symbol", "toStringTag", "value", "toXml", "jArray", "options", "indentation", "format", "indentBy", "length", "arrToStr", "arr", "jPath", "xmlStr", "isPreviousElementTag", "i", "tagObj", "tagName", "propName", "undefined", "new<PERSON><PERSON>", "textNodeName", "cdataPropName", "commentPropName", "newIdentation", "tagStart", "attr_to_str", "tagValue", "unpairedTags", "indexOf", "suppressUnpairedNode", "suppressEmptyNode", "endsWith", "includes", "attStr", "tempInd", "piTextNodeName", "tagText", "isStopNode", "replaceEntitiesValue", "tagValueProcessor", "keys", "attrMap", "attrStr", "ignoreAttributes", "attr", "attrVal", "attributeValueProcessor", "suppressBooleanAttributes", "substr", "attributeNamePrefix", "lastIndexOf", "index", "stopNodes", "textValue", "processEntities", "entities", "entity", "replace", "regex", "val", "defaultOptions", "attributesGroupName", "a", "attrName", "preserveOrder", "RegExp", "oneListGroup", "Builder", "assign", "isAttribute", "ignoreAttributesFn", "Array", "isArray", "_step", "_iterator", "_createForOfIteratorHelperLoose", "done", "pattern", "test", "attrPrefixLen", "processTextOrObjNode", "indentate", "tagEndChar", "newLine", "object", "level", "<PERSON><PERSON><PERSON><PERSON>", "result", "j2x", "concat", "buildTextValNode", "buildObjectNode", "repeat", "name", "startsWith", "build", "jObj", "buildFromOrderedJs", "arrayNodeName", "_jObj", "join", "Date", "buildAttrPairStr", "newval", "arr<PERSON>en", "listTagVal", "listTagAttr", "j", "item", "Ks", "L", "closeTag", "tagEndExp", "piClosingChar"], "sourceRoot": ""}