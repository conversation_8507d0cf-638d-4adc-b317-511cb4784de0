{"version": 3, "file": "VideoView.types.js", "sourceRoot": "", "sources": ["../src/VideoView.types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { ViewProps } from 'react-native';\n\nimport type { VideoPlayer } from './VideoPlayer.types';\n\n/**\n * Describes how a video should be scaled to fit in a container.\n * - `contain`: The video maintains its aspect ratio and fits inside the container, with possible letterboxing/pillarboxing.\n * - `cover`: The video maintains its aspect ratio and covers the entire container, potentially cropping some portions.\n * - `fill`: The video stretches/squeezes to completely fill the container, potentially causing distortion.\n */\nexport type VideoContentFit = 'contain' | 'cover' | 'fill';\n\n/**\n * Describes the type of the surface used to render the video.\n * - `surfaceView`: Uses the `SurfaceView` to render the video. This value should be used in the majority of cases. Provides significantly lower power consumption, better performance, and more features.\n * - `textureView`: Uses the `TextureView` to render the video. Should be used in cases where the SurfaceView is not supported or causes issues (for example, overlapping video views).\n *\n * You can learn more about surface types in the official [ExoPlayer documentation](https://developer.android.com/media/media3/ui/playerview#surfacetype).\n * @platform android\n */\nexport type SurfaceType = 'textureView' | 'surfaceView';\n\nexport interface VideoViewProps extends ViewProps {\n  /**\n   * A video player instance. Use [`useVideoPlayer()`](#usevideoplayersource-setup) hook to create one.\n   */\n  player: VideoPlayer;\n\n  /**\n   * Determines whether native controls should be displayed or not.\n   * @default true\n   */\n  nativeControls?: boolean;\n\n  /**\n   * Describes how the video should be scaled to fit in the container.\n   * Options are `'contain'`, `'cover'`, and `'fill'`.\n   * @default 'contain'\n   */\n  contentFit?: VideoContentFit;\n\n  /**\n   * Determines whether fullscreen mode is allowed or not.\n   * @default true\n   */\n  allowsFullscreen?: boolean;\n\n  /**\n   * Determines whether the timecodes should be displayed or not.\n   * @default true\n   * @platform ios\n   */\n  showsTimecodes?: boolean;\n\n  /**\n   * Determines whether the player allows the user to skip media content.\n   * @default false\n   * @platform android\n   * @platform ios\n   */\n  requiresLinearPlayback?: boolean;\n\n  /**\n   * Determines the type of the surface used to render the video.\n   * > This prop should not be changed at runtime.\n   * @default 'surfaceView'\n   * @platform android\n   */\n  surfaceType?: SurfaceType;\n\n  /**\n   * Determines the position offset of the video inside the container.\n   * @default { dx: 0, dy: 0 }\n   * @platform ios\n   */\n  contentPosition?: { dx?: number; dy?: number };\n\n  /**\n   * A callback to call after the video player enters Picture in Picture (PiP) mode.\n   * @platform android\n   * @platform ios\n   * @platform web\n   */\n  onPictureInPictureStart?: () => void;\n\n  /**\n   * A callback to call after the video player exits Picture in Picture (PiP) mode.\n   * @platform android\n   * @platform ios\n   * @platform web\n   */\n  onPictureInPictureStop?: () => void;\n\n  /**\n   * Determines whether the player allows Picture in Picture (PiP) mode.\n   * > **Note:** The `supportsPictureInPicture` property of the [config plugin](#configuration-in-app-config)\n   * > has to be configured for the PiP to work.\n   * @platform android\n   * @platform ios\n   * @platform web\n   */\n  allowsPictureInPicture?: boolean;\n\n  /**\n   * Determines whether a video should be played \"inline\", that is, within the element's playback area.\n   * @platform web\n   */\n  playsInline?: boolean;\n\n  /**\n   * Determines whether the player should start Picture in Picture (PiP) automatically when the app is in the background.\n   * > **Note:** Only one player can be in Picture in Picture (PiP) mode at a time.\n   *\n   * > **Note:** The `supportsPictureInPicture` property of the [config plugin](#configuration-in-app-config)\n   * > has to be configured for the PiP to work.\n   *\n   * @default false\n   * @platform android 12+\n   * @platform ios\n   */\n  startsPictureInPictureAutomatically?: boolean;\n\n  /**\n   * Specifies whether to perform video frame analysis (Live Text in videos).\n   * Check official [Apple documentation](https://developer.apple.com/documentation/avkit/avplayerviewcontroller/allowsvideoframeanalysis) for more details.\n   * @default true\n   * @platform ios 16.0+\n   */\n  allowsVideoFrameAnalysis?: boolean;\n\n  /**\n   * A callback to call after the video player enters fullscreen mode.\n   */\n  onFullscreenEnter?: () => void;\n\n  /**\n   * A callback to call after the video player exits fullscreen mode.\n   */\n  onFullscreenExit?: () => void;\n\n  /**\n   * A callback to call after the mounted `VideoPlayer` has rendered the first frame into the `VideoView`.\n   * This event can be used to hide any cover images that conceal the initial loading of the player.\n   * > **Note:** This event may also be called during playback when the current video track changes (for example when the player switches video quality).\n   */\n  onFirstFrameRender?: () => void;\n\n  /**\n   * Determines whether the player should use the default ExoPlayer shutter that covers the `VideoView` before the first video frame is rendered.\n   * Setting this property to `false` makes the Android behavior the same as iOS.\n   *\n   * @platform android\n   * @default false\n   */\n  useExoShutter?: boolean;\n\n  /**\n   * Determines the [cross origin policy](https://developer.mozilla.org/en-US/docs/Web/HTML/Reference/Attributes/crossorigin) used by the underlying native view on web.\n   * If undefined, does not use CORS at all.\n   *\n   * @platform web\n   * @default undefined\n   */\n  crossOrigin?: 'anonymous' | 'use-credentials';\n}\n"]}