{"version": 3, "file": "VideoModule.js", "sourceRoot": "", "sources": ["../src/VideoModule.ts"], "names": [], "mappings": "AAAA,OAAO,iBAAiB,MAAM,qBAAqB,CAAC;AAEpD;;;;;;GAMG;AACH,MAAM,UAAU,2BAA2B;IACzC,OAAO,iBAAiB,CAAC,2BAA2B,EAAE,CAAC;AACzD,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,oBAAoB;IAClC,OAAO,iBAAiB,CAAC,oBAAoB,EAAE,CAAC;AAClD,CAAC;AAED;;;;;;;;GAQG;AACH,MAAM,UAAU,sBAAsB,CAAC,SAAiB;IACtD,OAAO,iBAAiB,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;AAC7D,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,wBAAwB;IACtC,OAAO,iBAAiB,CAAC,wBAAwB,EAAE,CAAC;AACtD,CAAC", "sourcesContent": ["import NativeVideoModule from './NativeVideoModule';\n\n/**\n * Returns whether the current device supports Picture in Picture (PiP) mode.\n *\n * @returns A `boolean` which is `true` if the device supports PiP mode, and `false` otherwise.\n * @platform android\n * @platform ios\n */\nexport function isPictureInPictureSupported(): boolean {\n  return NativeVideoModule.isPictureInPictureSupported();\n}\n\n/**\n * Clears all video cache.\n * > This function can be called only if there are no existing `VideoPlayer` instances.\n *\n * @returns A promise that fulfills after the cache has been cleaned.\n * @platform android\n * @platform ios\n */\nexport function clearVideoCacheAsync(): Promise<void> {\n  return NativeVideoModule.clearVideoCacheAsync();\n}\n\n/**\n * Sets desired video cache size in bytes. The default video cache size is 1GB. Value set by this function is persistent.\n * The cache size is not guaranteed to be exact and the actual cache size may be slightly larger. The cache is evicted on a least-recently-used basis.\n * > This function can be called only if there are no existing `VideoPlayer` instances.\n *\n * @returns A promise that fulfills after the cache size has been set.\n * @platform android\n * @platform ios\n */\nexport function setVideoCacheSizeAsync(sizeBytes: number): Promise<void> {\n  return NativeVideoModule.setVideoCacheSizeAsync(sizeBytes);\n}\n\n/**\n * Returns the space currently occupied by the video cache in bytes.\n *\n * @platform android\n * @platform ios\n */\nexport function getCurrentVideoCacheSize(): number {\n  return NativeVideoModule.getCurrentVideoCacheSize();\n}\n"]}