import express from 'express';
import multer from 'multer';
import { protect } from '../middleware/auth.js';
import { 
  uploadToR2, 
  uploadProfileImage, 
  uploadCommunityImage, 
  uploadCourseThumbnail,
  validateImageFile,
  deleteFromR2 
} from '../utils/r2Storage.js';
import { User } from '../models/User.js';
import { Community } from '../models/Community.js';
import { Course } from '../models/Course.js';

const router = express.Router();

// Configure multer for memory storage
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    // Check file type
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'), false);
    }
  },
});

// @desc    Upload general image
// @route   POST /api/upload/image
// @access  Private
router.post('/image', protect, upload.single('image'), async (req, res, next) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'No image file provided'
      });
    }

    const { uploadType = 'general' } = req.body;

    // Validate image
    await validateImageFile(req.file.buffer);

    // Upload to R2
    const result = await uploadToR2(req.file.buffer, req.file.originalname, {
      folder: `uploads/${uploadType}`,
      optimize: true,
      createThumbnails: uploadType !== 'thumbnail',
      contentType: req.file.mimetype
    });

    res.status(200).json({
      success: true,
      message: 'Image uploaded successfully',
      data: result
    });

  } catch (error) {
    next(error);
  }
});

// @desc    Upload user profile image
// @route   POST /api/upload/profile
// @access  Private
router.post('/profile', protect, upload.single('image'), async (req, res, next) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'No image file provided'
      });
    }

    // Validate image
    await validateImageFile(req.file.buffer);

    // Upload profile image to R2
    const result = await uploadProfileImage(
      req.file.buffer, 
      req.file.originalname, 
      req.user.id
    );

    // Update user profile with new image URL
    const user = await User.findByIdAndUpdate(
      req.user.id,
      { profileImage: result.url },
      { new: true }
    ).select('-password');

    res.status(200).json({
      success: true,
      message: 'Profile image uploaded successfully',
      data: {
        ...result,
        user: {
          id: user._id,
          profileImage: user.profileImage
        }
      }
    });

  } catch (error) {
    next(error);
  }
});

// @desc    Upload community image (banner or icon)
// @route   POST /api/upload/community/:communityId
// @access  Private
router.post('/community/:communityId', protect, upload.single('image'), async (req, res, next) => {
  try {
    const { communityId } = req.params;
    const { imageType = 'banner' } = req.body; // 'banner' or 'icon'

    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'No image file provided'
      });
    }

    // Check if user has permission to upload to this community
    const community = await Community.findById(communityId);
    if (!community) {
      return res.status(404).json({
        success: false,
        error: 'Community not found'
      });
    }

    const isAdmin = community.admin.toString() === req.user.id;
    const isSubAdmin = community.subAdmins.includes(req.user.id);

    if (!isAdmin && !isSubAdmin) {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to upload images for this community'
      });
    }

    // Validate image
    await validateImageFile(req.file.buffer);

    // Upload community image to R2
    const result = await uploadCommunityImage(
      req.file.buffer,
      req.file.originalname,
      communityId,
      imageType
    );

    // Update community with new image URL
    const updateField = imageType === 'icon' ? 'iconImageUrl' : 'bannerImageurl';
    const updatedCommunity = await Community.findByIdAndUpdate(
      communityId,
      { [updateField]: result.url },
      { new: true }
    ).populate('admin', 'username name profileImage');

    res.status(200).json({
      success: true,
      message: `Community ${imageType} uploaded successfully`,
      data: {
        ...result,
        community: {
          id: updatedCommunity._id,
          [updateField]: updatedCommunity[updateField]
        }
      }
    });

  } catch (error) {
    next(error);
  }
});

// @desc    Upload course thumbnail
// @route   POST /api/upload/course/:courseId/thumbnail
// @access  Private
router.post('/course/:courseId/thumbnail', protect, upload.single('image'), async (req, res, next) => {
  try {
    const { courseId } = req.params;

    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'No image file provided'
      });
    }

    // Check if user has permission to upload to this course
    const course = await Course.findById(courseId);
    if (!course) {
      return res.status(404).json({
        success: false,
        error: 'Course not found'
      });
    }

    if (course.createdBy !== req.user.id) {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to upload images for this course'
      });
    }

    // Validate image
    await validateImageFile(req.file.buffer);

    // Upload course thumbnail to R2
    const result = await uploadCourseThumbnail(
      req.file.buffer,
      req.file.originalname,
      courseId
    );

    // Update course with new thumbnail URL
    const updatedCourse = await Course.findByIdAndUpdate(
      courseId,
      { thumbnail: result.url },
      { new: true }
    );

    res.status(200).json({
      success: true,
      message: 'Course thumbnail uploaded successfully',
      data: {
        ...result,
        course: {
          id: updatedCourse._id,
          thumbnail: updatedCourse.thumbnail
        }
      }
    });

  } catch (error) {
    next(error);
  }
});

// @desc    Delete image from R2
// @route   DELETE /api/upload/:fileName
// @access  Private
router.delete('/:fileName(*)', protect, async (req, res, next) => {
  try {
    const { fileName } = req.params;

    if (!fileName) {
      return res.status(400).json({
        success: false,
        error: 'No file name provided'
      });
    }

    // Basic security check - ensure user can only delete their own files
    // You might want to add more sophisticated permission checking
    const userFolder = `profiles/${req.user.id}`;
    if (!fileName.startsWith(userFolder) && !fileName.startsWith('uploads/general')) {
      return res.status(403).json({
        success: false,
        error: 'You do not have permission to delete this file'
      });
    }

    // Delete from R2
    await deleteFromR2(fileName);

    res.status(200).json({
      success: true,
      message: 'File deleted successfully'
    });

  } catch (error) {
    next(error);
  }
});

// @desc    Get upload status/info
// @route   GET /api/upload/status
// @access  Private
router.get('/status', protect, async (req, res) => {
  try {
    res.status(200).json({
      success: true,
      data: {
        maxFileSize: '10MB',
        allowedTypes: ['image/jpeg', 'image/png', 'image/webp'],
        r2Configured: !!(process.env.R2_ACCESS_KEY_ID && process.env.R2_SECRET_ACCESS_KEY),
        publicUrl: process.env.R2_PUBLIC_URL
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to get upload status'
    });
  }
});

// Error handling middleware for multer
router.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        error: 'File too large. Maximum size is 10MB.'
      });
    }
    
    return res.status(400).json({
      success: false,
      error: `Upload error: ${error.message}`
    });
  }
  
  next(error);
});

export default router;
